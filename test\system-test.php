<?php
/**
 * AstroGenix System Test Suite
 * Comprehensive testing for all platform functions
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

class AstroGenixSystemTest {
    private $db;
    private $testResults = [];
    private $errors = [];

    public function __construct() {
        $this->db = getDBConnection();
    }

    public function runAllTests() {
        echo "<h1>AstroGenix System Test Suite</h1>";
        echo "<style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .test-pass { color: green; font-weight: bold; }
            .test-fail { color: red; font-weight: bold; }
            .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
            .error-details { background: #ffe6e6; padding: 10px; margin: 5px 0; border-radius: 3px; }
        </style>";

        $this->testDatabaseConnection();
        $this->testUserFunctions();
        $this->testMiningPlans();
        $this->testReferralSystem();
        $this->testTaskSystem();
        $this->testAdminFunctions();
        $this->testFileStructure();
        $this->testMobileOptimization();
        $this->testAnimations();
        $this->testSecurity();

        $this->displayResults();
    }

    private function testDatabaseConnection() {
        echo "<div class='test-section'>";
        echo "<h2>Database Connection Tests</h2>";

        try {
            $result = $this->db->query("SELECT 1");
            $this->addResult("Database Connection", true, "Successfully connected to database");
        } catch (Exception $e) {
            $this->addResult("Database Connection", false, "Failed to connect: " . $e->getMessage());
        }

        // Test required tables
        $requiredTables = ['users', 'mining_plans', 'user_investments', 'referral_earnings', 'tasks', 'user_task_completions', 'activities'];
        
        foreach ($requiredTables as $table) {
            try {
                $result = $this->db->query("SHOW TABLES LIKE '$table'");
                $exists = $result->rowCount() > 0;
                $this->addResult("Table: $table", $exists, $exists ? "Table exists" : "Table missing");
            } catch (Exception $e) {
                $this->addResult("Table: $table", false, "Error checking table: " . $e->getMessage());
            }
        }

        echo "</div>";
    }

    private function testUserFunctions() {
        echo "<div class='test-section'>";
        echo "<h2>User Function Tests</h2>";

        // Test user registration functions
        $this->addResult("Function: generateReferralCode", function_exists('generateReferralCode'), "Function exists");
        $this->addResult("Function: isLoggedIn", function_exists('isLoggedIn'), "Function exists");
        $this->addResult("Function: getUserById", function_exists('getUserById'), "Function exists");
        $this->addResult("Function: updateUserBalance", function_exists('updateUserBalance'), "Function exists");

        // Test referral code generation
        if (function_exists('generateReferralCode')) {
            $code1 = generateReferralCode();
            $code2 = generateReferralCode();
            $this->addResult("Referral Code Generation", $code1 !== $code2, "Generates unique codes");
        }

        echo "</div>";
    }

    private function testMiningPlans() {
        echo "<div class='test-section'>";
        echo "<h2>Mining Plans Tests</h2>";

        try {
            $stmt = $this->db->query("SELECT COUNT(*) as count FROM mining_plans WHERE status = 'active'");
            $count = $stmt->fetch()['count'];
            $this->addResult("Active Mining Plans", $count > 0, "Found $count active mining plans");

            // Test mining plan categories
            $stmt = $this->db->query("SELECT DISTINCT category FROM mining_plans");
            $categories = $stmt->fetchAll(PDO::FETCH_COLUMN);
            $expectedCategories = ['solar', 'wind', 'hydro', 'geothermal', 'biomass', 'hybrid'];
            $hasAllCategories = count(array_intersect($categories, $expectedCategories)) >= 3;
            $this->addResult("Mining Categories", $hasAllCategories, "Has diverse mining categories");

        } catch (Exception $e) {
            $this->addResult("Mining Plans Query", false, "Error: " . $e->getMessage());
        }

        echo "</div>";
    }

    private function testReferralSystem() {
        echo "<div class='test-section'>";
        echo "<h2>Referral System Tests</h2>";

        // Test referral functions
        $this->addResult("Function: processReferralCommission", function_exists('processReferralCommission'), "Function exists");
        $this->addResult("Function: getReferralEarnings", function_exists('getReferralEarnings'), "Function exists");

        // Test referral table structure
        try {
            $stmt = $this->db->query("DESCRIBE referral_earnings");
            $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
            $requiredColumns = ['user_id', 'referral_user_id', 'level', 'commission_amount', 'investment_amount'];
            $hasAllColumns = count(array_intersect($columns, $requiredColumns)) == count($requiredColumns);
            $this->addResult("Referral Table Structure", $hasAllColumns, "Has all required columns");
        } catch (Exception $e) {
            $this->addResult("Referral Table Structure", false, "Error: " . $e->getMessage());
        }

        echo "</div>";
    }

    private function testTaskSystem() {
        echo "<div class='test-section'>";
        echo "<h2>Task System Tests</h2>";

        try {
            $stmt = $this->db->query("SELECT COUNT(*) as count FROM tasks WHERE status = 'active'");
            $count = $stmt->fetch()['count'];
            $this->addResult("Active Tasks", $count > 0, "Found $count active tasks");

            // Test task types
            $stmt = $this->db->query("SELECT DISTINCT task_type FROM tasks");
            $types = $stmt->fetchAll(PDO::FETCH_COLUMN);
            $expectedTypes = ['daily', 'weekly', 'monthly', 'one_time', 'referral', 'investment'];
            $hasVariousTypes = count(array_intersect($types, $expectedTypes)) >= 3;
            $this->addResult("Task Types", $hasVariousTypes, "Has various task types");

        } catch (Exception $e) {
            $this->addResult("Task System Query", false, "Error: " . $e->getMessage());
        }

        echo "</div>";
    }

    private function testAdminFunctions() {
        echo "<div class='test-section'>";
        echo "<h2>Admin Functions Tests</h2>";

        // Check admin files exist
        $adminFiles = [
            '../admin/index.php',
            '../admin/mining-plans.php',
            '../admin/users.php',
            '../admin/tasks.php'
        ];

        foreach ($adminFiles as $file) {
            $exists = file_exists($file);
            $this->addResult("Admin File: " . basename($file), $exists, $exists ? "File exists" : "File missing");
        }

        echo "</div>";
    }

    private function testFileStructure() {
        echo "<div class='test-section'>";
        echo "<h2>File Structure Tests</h2>";

        // Check critical files
        $criticalFiles = [
            '../index.php',
            '../pages/mining-plans.php',
            '../pages/referrals.php',
            '../pages/tasks.php',
            '../pages/dashboard.php',
            '../includes/header.php',
            '../includes/footer.php',
            '../assets/css/mobile.css',
            '../assets/css/animations.css',
            '../assets/js/mobile.js',
            '../assets/js/animations.js'
        ];

        foreach ($criticalFiles as $file) {
            $exists = file_exists($file);
            $this->addResult("File: " . basename($file), $exists, $exists ? "File exists" : "File missing");
        }

        echo "</div>";
    }

    private function testMobileOptimization() {
        echo "<div class='test-section'>";
        echo "<h2>Mobile Optimization Tests</h2>";

        // Check mobile CSS
        $mobileCss = file_get_contents('../assets/css/mobile.css');
        $this->addResult("Mobile CSS Loaded", !empty($mobileCss), "Mobile CSS file loaded");
        $this->addResult("Mobile Breakpoints", strpos($mobileCss, '@media (max-width: 767px)') !== false, "Has mobile breakpoints");
        $this->addResult("Touch Targets", strpos($mobileCss, '.touch-target') !== false, "Has touch target styles");

        // Check mobile JavaScript
        $mobileJs = file_get_contents('../assets/js/mobile.js');
        $this->addResult("Mobile JS Loaded", !empty($mobileJs), "Mobile JS file loaded");
        $this->addResult("Touch Events", strpos($mobileJs, 'touchstart') !== false, "Has touch event handling");

        echo "</div>";
    }

    private function testAnimations() {
        echo "<div class='test-section'>";
        echo "<h2>Animation Tests</h2>";

        // Check animation CSS
        $animationCss = file_get_contents('../assets/css/animations.css');
        $this->addResult("Animation CSS Loaded", !empty($animationCss), "Animation CSS file loaded");
        $this->addResult("Eco Animations", strpos($animationCss, '@keyframes eco-pulse') !== false, "Has eco-themed animations");
        $this->addResult("Mining Animations", strpos($animationCss, 'mining-progress') !== false, "Has mining progress animations");

        // Check animation JavaScript
        $animationJs = file_get_contents('../assets/js/animations.js');
        $this->addResult("Animation JS Loaded", !empty($animationJs), "Animation JS file loaded");
        $this->addResult("Particle System", strpos($animationJs, 'createParticles') !== false, "Has particle system");

        echo "</div>";
    }

    private function testSecurity() {
        echo "<div class='test-section'>";
        echo "<h2>Security Tests</h2>";

        // Check for SQL injection protection
        $configContent = file_get_contents('../config/config.php');
        $this->addResult("PDO Usage", strpos($configContent, 'PDO') !== false, "Uses PDO for database connections");

        // Check for XSS protection
        $headerContent = file_get_contents('../includes/header.php');
        $this->addResult("XSS Protection", strpos($headerContent, 'htmlspecialchars') !== false, "Uses XSS protection");

        // Check for CSRF protection
        $hasCSRF = strpos($headerContent, 'csrf') !== false || strpos($headerContent, 'token') !== false;
        $this->addResult("CSRF Protection", $hasCSRF, $hasCSRF ? "Has CSRF protection" : "Consider adding CSRF protection");

        echo "</div>";
    }

    private function addResult($test, $passed, $message) {
        $this->testResults[] = [
            'test' => $test,
            'passed' => $passed,
            'message' => $message
        ];

        $status = $passed ? "<span class='test-pass'>PASS</span>" : "<span class='test-fail'>FAIL</span>";
        echo "<div>$status - $test: $message</div>";

        if (!$passed) {
            $this->errors[] = $test;
        }
    }

    private function displayResults() {
        echo "<div class='test-section'>";
        echo "<h2>Test Summary</h2>";

        $totalTests = count($this->testResults);
        $passedTests = count(array_filter($this->testResults, function($result) {
            return $result['passed'];
        }));
        $failedTests = $totalTests - $passedTests;

        echo "<p><strong>Total Tests:</strong> $totalTests</p>";
        echo "<p><strong>Passed:</strong> <span class='test-pass'>$passedTests</span></p>";
        echo "<p><strong>Failed:</strong> <span class='test-fail'>$failedTests</span></p>";

        if ($failedTests > 0) {
            echo "<h3>Failed Tests:</h3>";
            echo "<div class='error-details'>";
            foreach ($this->errors as $error) {
                echo "<div>• $error</div>";
            }
            echo "</div>";
        } else {
            echo "<p><strong style='color: green;'>🎉 All tests passed! AstroGenix platform is ready for production.</strong></p>";
        }

        echo "</div>";
    }
}

// Run tests
$tester = new AstroGenixSystemTest();
$tester->runAllTests();
?>
