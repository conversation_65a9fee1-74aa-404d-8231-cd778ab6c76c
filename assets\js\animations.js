/**
 * AstroGenix Eco-Mining Interactive Animations
 * Advanced animation system for eco-mining platform
 */

class EcoAnimations {
    constructor() {
        this.particles = [];
        this.animationFrame = null;
        this.isAnimating = false;
        this.init();
    }

    init() {
        this.setupParticleSystem();
        this.setupScrollAnimations();
        this.setupHoverEffects();
        this.setupCounterAnimations();
        this.setupMiningProgressAnimations();
        this.setupEnergyFlowAnimations();
    }

    setupParticleSystem() {
        const containers = document.querySelectorAll('.particle-container');
        
        containers.forEach(container => {
            this.createParticles(container, 15);
        });

        // Create floating particles for hero section
        const heroSection = document.querySelector('.hero-section');
        if (heroSection) {
            this.createFloatingParticles(heroSection, 25);
        }
    }

    createParticles(container, count) {
        for (let i = 0; i < count; i++) {
            const particle = document.createElement('div');
            particle.className = 'eco-particle';
            
            // Random positioning
            particle.style.left = Math.random() * 100 + '%';
            particle.style.top = Math.random() * 100 + '%';
            
            // Random animation delay
            particle.style.animationDelay = Math.random() * 6 + 's';
            
            // Random size variation
            const size = 2 + Math.random() * 4;
            particle.style.width = size + 'px';
            particle.style.height = size + 'px';
            
            container.appendChild(particle);
        }
    }

    createFloatingParticles(container, count) {
        const particleContainer = document.createElement('div');
        particleContainer.className = 'particle-container';
        container.appendChild(particleContainer);

        for (let i = 0; i < count; i++) {
            const particle = document.createElement('div');
            particle.className = 'eco-particle floating-particles';
            
            // Random positioning
            particle.style.left = Math.random() * 100 + '%';
            particle.style.top = Math.random() * 100 + '%';
            
            // Random animation properties
            particle.style.animationDelay = Math.random() * 4 + 's';
            particle.style.animationDuration = (4 + Math.random() * 4) + 's';
            
            particleContainer.appendChild(particle);
        }
    }

    setupScrollAnimations() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.triggerElementAnimation(entry.target);
                }
            });
        }, observerOptions);

        // Observe mining cards
        document.querySelectorAll('.mining-card').forEach(card => {
            observer.observe(card);
        });

        // Observe stat cards
        document.querySelectorAll('.eco-stat').forEach(stat => {
            observer.observe(stat);
        });

        // Observe progress bars
        document.querySelectorAll('.eco-progress-bar').forEach(bar => {
            observer.observe(bar);
        });
    }

    triggerElementAnimation(element) {
        if (element.classList.contains('mining-card')) {
            this.animateMiningCard(element);
        } else if (element.classList.contains('eco-stat')) {
            this.animateStatCard(element);
        } else if (element.classList.contains('eco-progress-bar')) {
            this.animateProgressBar(element);
        }
    }

    animateMiningCard(card) {
        // Add entrance animation
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.6s ease-out';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
            
            // Animate icon
            const icon = card.querySelector('.eco-icon');
            if (icon) {
                setTimeout(() => {
                    icon.classList.add('leaf-growth');
                }, 300);
            }

            // Animate progress if exists
            const progress = card.querySelector('.mining-progress');
            if (progress) {
                setTimeout(() => {
                    this.animateMiningProgress(progress);
                }, 600);
            }
        }, 100);
    }

    animateStatCard(card) {
        const counter = card.querySelector('.stat-value');
        const icon = card.querySelector('.stat-icon');
        
        if (counter) {
            this.animateCounter(counter);
        }
        
        if (icon) {
            icon.classList.add('eco-pulse');
        }
    }

    animateProgressBar(bar) {
        const progress = bar.getAttribute('data-progress') || '0';
        bar.style.width = '0%';
        
        setTimeout(() => {
            bar.style.transition = 'width 2s ease-out';
            bar.style.width = progress + '%';
        }, 200);
    }

    animateCounter(element) {
        const target = parseInt(element.getAttribute('data-target')) || 0;
        const duration = 2000;
        const start = 0;
        const increment = target / (duration / 16);
        let current = start;

        const updateCounter = () => {
            current += increment;
            if (current < target) {
                element.textContent = Math.floor(current).toLocaleString();
                requestAnimationFrame(updateCounter);
            } else {
                element.textContent = target.toLocaleString();
            }
        };

        updateCounter();
    }

    setupHoverEffects() {
        // Mining card hover effects
        document.querySelectorAll('.mining-card').forEach(card => {
            card.addEventListener('mouseenter', () => {
                this.triggerHoverAnimation(card);
            });

            card.addEventListener('mouseleave', () => {
                this.resetHoverAnimation(card);
            });
        });

        // Button hover effects
        document.querySelectorAll('.energy-button').forEach(button => {
            button.addEventListener('mouseenter', () => {
                this.createEnergyPulse(button);
            });
        });
    }

    triggerHoverAnimation(card) {
        // Add glow effect
        card.classList.add('eco-glow');
        
        // Animate particles
        const particles = card.querySelectorAll('.eco-particle');
        particles.forEach(particle => {
            particle.style.animationDuration = '2s';
        });

        // Create energy flow
        this.createEnergyFlow(card);
    }

    resetHoverAnimation(card) {
        card.classList.remove('eco-glow');
        
        const particles = card.querySelectorAll('.eco-particle');
        particles.forEach(particle => {
            particle.style.animationDuration = '4s';
        });
    }

    createEnergyFlow(element) {
        const energyOrb = document.createElement('div');
        energyOrb.className = 'energy-orb';
        energyOrb.style.cssText = `
            position: absolute;
            width: 8px;
            height: 8px;
            background: radial-gradient(circle, #10b981, #34d399);
            border-radius: 50%;
            top: 50%;
            left: 0;
            z-index: 10;
            animation: energy-flow 2s linear;
        `;

        element.style.position = 'relative';
        element.appendChild(energyOrb);

        setTimeout(() => {
            energyOrb.remove();
        }, 2000);
    }

    createEnergyPulse(button) {
        const pulse = document.createElement('div');
        pulse.style.cssText = `
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(16, 185, 129, 0.3);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            animation: eco-pulse 0.6s ease-out;
            pointer-events: none;
        `;

        button.style.position = 'relative';
        button.appendChild(pulse);

        setTimeout(() => {
            pulse.remove();
        }, 600);
    }

    setupCounterAnimations() {
        // Animate profit counters
        document.querySelectorAll('.profit-display').forEach(display => {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.animateProfitCounter(entry.target);
                        observer.unobserve(entry.target);
                    }
                });
            });
            observer.observe(display);
        });
    }

    animateProfitCounter(element) {
        const digits = element.textContent.split('');
        element.innerHTML = '';

        digits.forEach((digit, index) => {
            const span = document.createElement('span');
            span.className = 'digit';
            span.textContent = digit;
            span.style.animationDelay = (index * 0.1) + 's';
            element.appendChild(span);
        });
    }

    setupMiningProgressAnimations() {
        document.querySelectorAll('.mining-equipment').forEach(equipment => {
            const type = equipment.getAttribute('data-type');
            
            switch(type) {
                case 'solar':
                    this.animateSolarPanel(equipment);
                    break;
                case 'wind':
                    this.animateWindTurbine(equipment);
                    break;
                case 'hydro':
                    this.animateHydroFlow(equipment);
                    break;
            }
        });
    }

    animateSolarPanel(panel) {
        panel.classList.add('solar-panel');
        
        // Create solar rays effect
        setInterval(() => {
            const ray = document.createElement('div');
            ray.style.cssText = `
                position: absolute;
                width: 2px;
                height: 20px;
                background: linear-gradient(to bottom, #fbbf24, transparent);
                top: -20px;
                left: ${Math.random() * 100}%;
                animation: carbon-absorption 1s ease-in forwards;
            `;
            panel.appendChild(ray);
            
            setTimeout(() => ray.remove(), 1000);
        }, 500);
    }

    animateWindTurbine(turbine) {
        const blades = turbine.querySelectorAll('.turbine-blade');
        blades.forEach(blade => {
            blade.classList.add('wind-turbine-blade');
        });
    }

    animateHydroFlow(hydro) {
        hydro.classList.add('hydro-flow');
    }

    setupEnergyFlowAnimations() {
        // Create continuous energy flow between mining cards
        setInterval(() => {
            this.createGlobalEnergyFlow();
        }, 3000);
    }

    createGlobalEnergyFlow() {
        const cards = document.querySelectorAll('.mining-card');
        if (cards.length < 2) return;

        const startCard = cards[Math.floor(Math.random() * cards.length)];
        const endCard = cards[Math.floor(Math.random() * cards.length)];

        if (startCard === endCard) return;

        const energyBeam = document.createElement('div');
        energyBeam.style.cssText = `
            position: fixed;
            width: 2px;
            height: 2px;
            background: #10b981;
            border-radius: 50%;
            z-index: 1000;
            pointer-events: none;
            box-shadow: 0 0 10px #10b981;
        `;

        document.body.appendChild(energyBeam);

        const startRect = startCard.getBoundingClientRect();
        const endRect = endCard.getBoundingClientRect();

        energyBeam.style.left = (startRect.left + startRect.width / 2) + 'px';
        energyBeam.style.top = (startRect.top + startRect.height / 2) + 'px';

        const deltaX = (endRect.left + endRect.width / 2) - (startRect.left + startRect.width / 2);
        const deltaY = (endRect.top + endRect.height / 2) - (startRect.top + startRect.height / 2);

        energyBeam.style.transition = 'all 1s ease-in-out';
        energyBeam.style.transform = `translate(${deltaX}px, ${deltaY}px)`;

        setTimeout(() => {
            energyBeam.remove();
        }, 1000);
    }

    // Utility methods
    static triggerCarbonAbsorption(element) {
        const molecules = element.querySelectorAll('.carbon-molecule');
        molecules.forEach((molecule, index) => {
            setTimeout(() => {
                molecule.classList.add('absorbed');
            }, index * 200);
        });
    }

    static updateEcoScore(element, newScore) {
        const currentScore = parseInt(element.textContent) || 0;
        const increment = (newScore - currentScore) / 30;
        let current = currentScore;

        const updateScore = () => {
            current += increment;
            if (current < newScore) {
                element.textContent = Math.floor(current);
                requestAnimationFrame(updateScore);
            } else {
                element.textContent = newScore;
                element.classList.add('eco-pulse');
                setTimeout(() => element.classList.remove('eco-pulse'), 2000);
            }
        };

        updateScore();
    }
}

// Initialize animations when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    new EcoAnimations();
});

// Export for global use
window.EcoAnimations = EcoAnimations;
