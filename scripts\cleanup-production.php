<?php
/**
 * AstroGenix Production Cleanup Script
 * Removes debug files and optimizes for production
 */

class ProductionCleanup {
    private $projectRoot;
    private $filesToRemove = [];
    private $foldersToRemove = [];
    private $optimizedFiles = 0;

    public function __construct() {
        $this->projectRoot = dirname(__DIR__);
        $this->setupCleanupLists();
    }

    private function setupCleanupLists() {
        // Files to remove in production
        $this->filesToRemove = [
            'test/system-test.php',
            'scripts/cleanup-production.php', // Remove this script after running
            '.env.example',
            'README.md',
            'CHANGELOG.md',
            'composer.json',
            'package.json',
            '.gitignore',
            '.git',
            'node_modules',
            'vendor/bin',
            'logs/debug.log',
            'logs/test.log'
        ];

        // Folders to remove in production
        $this->foldersToRemove = [
            'test',
            'docs',
            '.git',
            'node_modules',
            'vendor/phpunit',
            'vendor/mockery'
        ];
    }

    public function cleanup() {
        echo "<h1>AstroGenix Production Cleanup</h1>";
        echo "<style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .success { color: green; }
            .warning { color: orange; }
            .error { color: red; }
            .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        </style>";

        $this->removeDebugFiles();
        $this->removeTestFiles();
        $this->optimizeCSS();
        $this->optimizeJS();
        $this->cleanupDatabase();
        $this->setProductionPermissions();
        $this->generateProductionReport();
    }

    private function removeDebugFiles() {
        echo "<div class='section'>";
        echo "<h2>Removing Debug Files</h2>";

        foreach ($this->filesToRemove as $file) {
            $fullPath = $this->projectRoot . '/' . $file;
            
            if (file_exists($fullPath)) {
                if (is_dir($fullPath)) {
                    $this->removeDirectory($fullPath);
                    echo "<div class='success'>✓ Removed directory: $file</div>";
                } else {
                    unlink($fullPath);
                    echo "<div class='success'>✓ Removed file: $file</div>";
                }
            } else {
                echo "<div class='warning'>⚠ File not found: $file</div>";
            }
        }

        echo "</div>";
    }

    private function removeTestFiles() {
        echo "<div class='section'>";
        echo "<h2>Removing Test Files</h2>";

        $testPatterns = [
            '*.test.php',
            '*_test.php',
            'test_*.php',
            '*.spec.php'
        ];

        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($this->projectRoot)
        );

        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $filename = $file->getFilename();
                
                foreach ($testPatterns as $pattern) {
                    if (fnmatch($pattern, $filename)) {
                        unlink($file->getPathname());
                        echo "<div class='success'>✓ Removed test file: " . $file->getRelativePathname() . "</div>";
                        break;
                    }
                }
            }
        }

        echo "</div>";
    }

    private function optimizeCSS() {
        echo "<div class='section'>";
        echo "<h2>Optimizing CSS Files</h2>";

        $cssFiles = $this->findFiles('*.css');
        
        foreach ($cssFiles as $file) {
            $content = file_get_contents($file);
            $originalSize = strlen($content);
            
            // Remove comments
            $content = preg_replace('/\/\*.*?\*\//s', '', $content);
            
            // Remove extra whitespace
            $content = preg_replace('/\s+/', ' ', $content);
            $content = str_replace(['; ', ' {', '{ ', ' }', '} ', ': '], [';', '{', '{', '}', '}', ':'], $content);
            
            // Remove trailing semicolons before }
            $content = str_replace(';}', '}', $content);
            
            $newSize = strlen($content);
            $savings = $originalSize - $newSize;
            $percentage = round(($savings / $originalSize) * 100, 1);
            
            file_put_contents($file, $content);
            echo "<div class='success'>✓ Optimized: " . basename($file) . " (saved {$savings} bytes, {$percentage}%)</div>";
            $this->optimizedFiles++;
        }

        echo "</div>";
    }

    private function optimizeJS() {
        echo "<div class='section'>";
        echo "<h2>Optimizing JavaScript Files</h2>";

        $jsFiles = $this->findFiles('*.js');
        
        foreach ($jsFiles as $file) {
            $content = file_get_contents($file);
            $originalSize = strlen($content);
            
            // Remove single-line comments
            $content = preg_replace('/\/\/.*$/m', '', $content);
            
            // Remove multi-line comments
            $content = preg_replace('/\/\*.*?\*\//s', '', $content);
            
            // Remove extra whitespace
            $content = preg_replace('/\s+/', ' ', $content);
            $content = str_replace(['; ', ' {', '{ ', ' }', '} ', ': ', ', '], [';', '{', '{', '}', '}', ':', ','], $content);
            
            $newSize = strlen($content);
            $savings = $originalSize - $newSize;
            $percentage = round(($savings / $originalSize) * 100, 1);
            
            file_put_contents($file, $content);
            echo "<div class='success'>✓ Optimized: " . basename($file) . " (saved {$savings} bytes, {$percentage}%)</div>";
            $this->optimizedFiles++;
        }

        echo "</div>";
    }

    private function cleanupDatabase() {
        echo "<div class='section'>";
        echo "<h2>Database Cleanup</h2>";

        try {
            require_once $this->projectRoot . '/config/config.php';
            $pdo = getDBConnection();

            // Remove test data
            $pdo->exec("DELETE FROM activities WHERE description LIKE '%test%' OR description LIKE '%debug%'");
            echo "<div class='success'>✓ Removed test activities</div>";

            // Remove old sessions
            $pdo->exec("DELETE FROM user_sessions WHERE last_activity < DATE_SUB(NOW(), INTERVAL 30 DAY)");
            echo "<div class='success'>✓ Cleaned old sessions</div>";

            // Optimize tables
            $tables = ['users', 'mining_plans', 'user_investments', 'referral_earnings', 'tasks', 'user_task_completions', 'activities'];
            foreach ($tables as $table) {
                $pdo->exec("OPTIMIZE TABLE $table");
                echo "<div class='success'>✓ Optimized table: $table</div>";
            }

        } catch (Exception $e) {
            echo "<div class='error'>✗ Database cleanup failed: " . $e->getMessage() . "</div>";
        }

        echo "</div>";
    }

    private function setProductionPermissions() {
        echo "<div class='section'>";
        echo "<h2>Setting Production Permissions</h2>";

        // Set secure permissions
        $this->setPermissions($this->projectRoot, 0755, 0644);
        
        // Special permissions for writable directories
        $writableDirs = ['logs', 'uploads', 'cache', 'backups'];
        foreach ($writableDirs as $dir) {
            $fullPath = $this->projectRoot . '/' . $dir;
            if (is_dir($fullPath)) {
                chmod($fullPath, 0755);
                echo "<div class='success'>✓ Set writable permissions: $dir</div>";
            }
        }

        // Protect sensitive files
        $sensitiveFiles = ['config/config.php', 'config/production.php'];
        foreach ($sensitiveFiles as $file) {
            $fullPath = $this->projectRoot . '/' . $file;
            if (file_exists($fullPath)) {
                chmod($fullPath, 0600);
                echo "<div class='success'>✓ Protected sensitive file: $file</div>";
            }
        }

        echo "</div>";
    }

    private function generateProductionReport() {
        echo "<div class='section'>";
        echo "<h2>Production Cleanup Report</h2>";

        $report = [
            'cleanup_date' => date('Y-m-d H:i:s'),
            'files_optimized' => $this->optimizedFiles,
            'project_size' => $this->getDirectorySize($this->projectRoot),
            'php_version' => PHP_VERSION,
            'environment' => 'production'
        ];

        echo "<div class='success'>";
        echo "<h3>Cleanup Summary:</h3>";
        echo "<ul>";
        echo "<li>Cleanup completed: " . $report['cleanup_date'] . "</li>";
        echo "<li>Files optimized: " . $report['files_optimized'] . "</li>";
        echo "<li>Project size: " . $this->formatBytes($report['project_size']) . "</li>";
        echo "<li>PHP version: " . $report['php_version'] . "</li>";
        echo "</ul>";
        echo "</div>";

        // Save report
        file_put_contents($this->projectRoot . '/production-report.json', json_encode($report, JSON_PRETTY_PRINT));
        echo "<div class='success'>✓ Production report saved</div>";

        echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px; margin-top: 20px;'>";
        echo "<h3 style='color: #2d5a2d;'>🎉 AstroGenix is Production Ready!</h3>";
        echo "<p><strong>Next Steps:</strong></p>";
        echo "<ol>";
        echo "<li>Update database credentials in config/production.php</li>";
        echo "<li>Configure your web server (Apache/Nginx)</li>";
        echo "<li>Set up SSL certificate</li>";
        echo "<li>Configure backup system</li>";
        echo "<li>Set up monitoring</li>";
        echo "</ol>";
        echo "</div>";

        echo "</div>";
    }

    private function findFiles($pattern) {
        $files = [];
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($this->projectRoot)
        );

        foreach ($iterator as $file) {
            if ($file->isFile() && fnmatch($pattern, $file->getFilename())) {
                $files[] = $file->getPathname();
            }
        }

        return $files;
    }

    private function removeDirectory($dir) {
        if (!is_dir($dir)) return;

        $files = array_diff(scandir($dir), ['.', '..']);
        foreach ($files as $file) {
            $path = $dir . '/' . $file;
            is_dir($path) ? $this->removeDirectory($path) : unlink($path);
        }
        rmdir($dir);
    }

    private function setPermissions($dir, $dirPerm, $filePerm) {
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($dir)
        );

        foreach ($iterator as $item) {
            if ($item->isDir()) {
                chmod($item->getPathname(), $dirPerm);
            } else {
                chmod($item->getPathname(), $filePerm);
            }
        }
    }

    private function getDirectorySize($dir) {
        $size = 0;
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($dir)
        );

        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $size += $file->getSize();
            }
        }

        return $size;
    }

    private function formatBytes($size, $precision = 2) {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
            $size /= 1024;
        }
        
        return round($size, $precision) . ' ' . $units[$i];
    }
}

// Run cleanup
$cleanup = new ProductionCleanup();
$cleanup->cleanup();
?>
