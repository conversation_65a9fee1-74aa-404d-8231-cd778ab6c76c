<?php
require_once '../config/config.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

requireAdmin();

$page_title = 'Управление майнинг-планами';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $action = $_POST['action'] ?? '';
        
        if ($action === 'add') {
            $title = sanitizeInput($_POST['title'] ?? '');
            $description = sanitizeInput($_POST['description'] ?? '');
            $category = sanitizeInput($_POST['category'] ?? '');
            $min_amount = floatval($_POST['min_amount'] ?? 0);
            $max_amount = floatval($_POST['max_amount'] ?? 0);
            $daily_rate = floatval($_POST['daily_rate'] ?? 0);
            $duration_days = intval($_POST['duration_days'] ?? 0);
            $icon_class = sanitizeInput($_POST['icon_class'] ?? 'fas fa-leaf');
            $is_active = isset($_POST['is_active']) ? 1 : 0;
            
            if ($title && $description && $min_amount > 0 && $daily_rate > 0) {
                $db = getDB();
                $stmt = $db->prepare("
                    INSERT INTO mining_plans (title, description, category, min_amount, max_amount, daily_rate, duration_days, icon_class, is_active) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ");
                
                if ($stmt->execute([$title, $description, $category, $min_amount, $max_amount, $daily_rate, $duration_days, $icon_class, $is_active])) {
                    setFlashMessage('success', 'Майнинг-план успешно добавлен');
                } else {
                    setFlashMessage('error', 'Ошибка при добавлении майнинг-плана');
                }
            } else {
                setFlashMessage('error', 'Пожалуйста, заполните все обязательные поля');
            }
        }
        
        elseif ($action === 'edit') {
            $id = intval($_POST['id'] ?? 0);
            $title = sanitizeInput($_POST['title'] ?? '');
            $description = sanitizeInput($_POST['description'] ?? '');
            $category = sanitizeInput($_POST['category'] ?? '');
            $min_amount = floatval($_POST['min_amount'] ?? 0);
            $max_amount = floatval($_POST['max_amount'] ?? 0);
            $daily_rate = floatval($_POST['daily_rate'] ?? 0);
            $duration_days = intval($_POST['duration_days'] ?? 0);
            $icon_class = sanitizeInput($_POST['icon_class'] ?? 'fas fa-leaf');
            $is_active = isset($_POST['is_active']) ? 1 : 0;
            
            if ($id && $title && $description && $min_amount > 0 && $daily_rate > 0) {
                $db = getDB();
                $stmt = $db->prepare("
                    UPDATE mining_plans 
                    SET title = ?, description = ?, category = ?, min_amount = ?, max_amount = ?, 
                        daily_rate = ?, duration_days = ?, icon_class = ?, is_active = ?
                    WHERE id = ?
                ");
                
                if ($stmt->execute([$title, $description, $category, $min_amount, $max_amount, $daily_rate, $duration_days, $icon_class, $is_active, $id])) {
                    setFlashMessage('success', 'Майнинг-план успешно обновлен');
                } else {
                    setFlashMessage('error', 'Ошибка при обновлении майнинг-плана');
                }
            } else {
                setFlashMessage('error', 'Пожалуйста, заполните все обязательные поля');
            }
        }
        
        elseif ($action === 'toggle_status') {
            $id = intval($_POST['id'] ?? 0);
            if ($id) {
                $db = getDB();
                $stmt = $db->prepare("UPDATE mining_plans SET is_active = NOT is_active WHERE id = ?");
                if ($stmt->execute([$id])) {
                    setFlashMessage('success', 'Статус майнинг-плана изменен');
                } else {
                    setFlashMessage('error', 'Ошибка при изменении статуса');
                }
            }
        }
        
        elseif ($action === 'delete') {
            $id = intval($_POST['id'] ?? 0);
            if ($id) {
                $db = getDB();
                
                // Check if mining plan has active user investments
                $stmt = $db->prepare("SELECT COUNT(*) as count FROM user_mining_investments WHERE mining_plan_id = ? AND is_active = 1");
                $stmt->execute([$id]);
                $active_count = $stmt->fetch()['count'];
                
                if ($active_count > 0) {
                    setFlashMessage('error', 'Нельзя удалить майнинг-план с активными инвестициями пользователей');
                } else {
                    $stmt = $db->prepare("DELETE FROM mining_plans WHERE id = ?");
                    if ($stmt->execute([$id])) {
                        setFlashMessage('success', 'Майнинг-план успешно удален');
                    } else {
                        setFlashMessage('error', 'Ошибка при удалении майнинг-плана');
                    }
                }
            }
        }
    }
    
    redirect('mining-plans.php');
}

// Get all mining plans
$db = getDB();
$stmt = $db->prepare("
    SELECT mp.*, 
           COUNT(umi.id) as total_miners,
           SUM(umi.amount) as total_invested
    FROM mining_plans mp
    LEFT JOIN user_mining_investments umi ON mp.id = umi.mining_plan_id AND umi.is_active = 1
    GROUP BY mp.id
    ORDER BY mp.created_at DESC
");
$stmt->execute();
$mining_plans = $stmt->fetchAll();

// Get mining plan for editing if requested
$edit_plan = null;
if (isset($_GET['edit'])) {
    $edit_id = intval($_GET['edit']);
    $stmt = $db->prepare("SELECT * FROM mining_plans WHERE id = ?");
    $stmt->execute([$edit_id]);
    $edit_plan = $stmt->fetch();
}

include '../includes/header.php';
?>

<div class="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Управление майнинг-планами</h1>
                    <p class="text-gray-600 mt-2">Создавайте и управляйте экологическими майнинг-планами</p>
                </div>
                <button onclick="openAddModal()" class="bg-gradient-to-r from-green-500 to-teal-600 hover:from-green-600 hover:to-teal-700 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg">
                    <i class="fas fa-plus mr-2"></i>Добавить план
                </button>
            </div>
        </div>

        <!-- Flash Messages -->
        <?php displayFlashMessages(); ?>

        <!-- Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="bg-white rounded-xl p-6 card-shadow border border-gray-100 group hover:shadow-xl transition-all duration-300">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-teal-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <i class="fas fa-leaf text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Всего планов</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo count($mining_plans); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 card-shadow border border-gray-100 group hover:shadow-xl transition-all duration-300">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <i class="fas fa-users text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Всего майнеров</p>
                        <p class="text-2xl font-bold text-gray-900">
                            <?php echo array_sum(array_column($mining_plans, 'total_miners')); ?>
                        </p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 card-shadow border border-gray-100 group hover:shadow-xl transition-all duration-300">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <i class="fas fa-dollar-sign text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Общая сумма</p>
                        <p class="text-2xl font-bold text-gray-900">
                            <?php echo formatCurrency(array_sum(array_column($mining_plans, 'total_invested'))); ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mining Plans List -->
        <div class="bg-white rounded-xl card-shadow border border-gray-100">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-xl font-bold text-gray-900">Список майнинг-планов</h2>
            </div>
            <div class="p-6">
                <?php if (empty($mining_plans)): ?>
                    <div class="text-center py-8">
                        <i class="fas fa-leaf text-gray-400 text-4xl mb-4"></i>
                        <p class="text-gray-600">Пока нет созданных майнинг-планов</p>
                        <button onclick="openAddModal()" class="mt-4 bg-gradient-to-r from-green-500 to-teal-600 hover:from-green-600 hover:to-teal-700 text-white px-6 py-2 rounded-lg font-medium transition-all duration-300 transform hover:scale-105 shadow-lg">
                            Создать первый план
                        </button>
                    </div>
                <?php else: ?>
                    <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                        <?php foreach ($mining_plans as $plan): ?>
                            <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-300 <?php echo $plan['is_active'] ? 'bg-white' : 'bg-gray-50'; ?>">
                                <div class="flex justify-between items-start mb-3">
                                    <div class="flex-1">
                                        <h3 class="font-semibold text-gray-900 mb-1"><?php echo htmlspecialchars($plan['title']); ?></h3>
                                        <p class="text-sm text-gray-600 mb-2"><?php echo htmlspecialchars(substr($plan['description'], 0, 100)) . '...'; ?></p>
                                        <span class="inline-block px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                                            <?php echo ucfirst(str_replace('_', ' ', $plan['category'])); ?>
                                        </span>
                                    </div>
                                    <div class="ml-2">
                                        <i class="<?php echo htmlspecialchars($plan['icon_class']); ?> text-2xl text-green-500"></i>
                                    </div>
                                </div>

                                <div class="grid grid-cols-2 gap-2 text-xs text-gray-600 mb-3">
                                    <div>
                                        <span class="font-medium">Мин. сумма:</span><br>
                                        $<?php echo number_format($plan['min_amount']); ?>
                                    </div>
                                    <div>
                                        <span class="font-medium">Дневная ставка:</span><br>
                                        <?php echo number_format($plan['daily_rate'], 2); ?>%
                                    </div>
                                    <div>
                                        <span class="font-medium">Майнеров:</span><br>
                                        <?php echo number_format($plan['total_miners']); ?>
                                    </div>
                                    <div>
                                        <span class="font-medium">Инвестировано:</span><br>
                                        $<?php echo number_format($plan['total_invested']); ?>
                                    </div>
                                </div>

                                <div class="flex justify-between items-center">
                                    <span class="text-xs px-2 py-1 rounded-full <?php echo $plan['is_active'] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'; ?>">
                                        <?php echo $plan['is_active'] ? 'Активен' : 'Неактивен'; ?>
                                    </span>

                                    <div class="flex space-x-1">
                                        <button onclick="editPlan(<?php echo htmlspecialchars(json_encode($plan)); ?>)"
                                                class="bg-blue-500 text-white px-2 py-1 rounded text-xs hover:bg-blue-600 transition-colors">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <form method="POST" class="inline" onsubmit="return confirm('Изменить статус плана?')">
                                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                            <input type="hidden" name="action" value="toggle_status">
                                            <input type="hidden" name="id" value="<?php echo $plan['id']; ?>">
                                            <button type="submit" class="<?php echo $plan['is_active'] ? 'bg-yellow-500 hover:bg-yellow-600' : 'bg-green-500 hover:bg-green-600'; ?> text-white px-2 py-1 rounded text-xs transition-colors">
                                                <i class="fas <?php echo $plan['is_active'] ? 'fa-pause' : 'fa-play'; ?>"></i>
                                            </button>
                                        </form>
                                        <form method="POST" class="inline" onsubmit="return confirm('Удалить план? Это действие нельзя отменить.')">
                                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                            <input type="hidden" name="action" value="delete">
                                            <input type="hidden" name="id" value="<?php echo $plan['id']; ?>">
                                            <button type="submit" class="bg-red-500 text-white px-2 py-1 rounded text-xs hover:bg-red-600 transition-colors">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Add/Edit Mining Plan Modal -->
<div id="planModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
    <div class="bg-white rounded-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div class="p-6 border-b border-gray-200">
            <h3 id="modalTitle" class="text-xl font-bold text-gray-900">Добавить майнинг-план</h3>
        </div>
        <div class="p-6">
            <form method="POST" id="planForm">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                <input type="hidden" name="action" id="formAction" value="add">
                <input type="hidden" name="id" id="planId" value="">

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Название плана *</label>
                        <input type="text" name="title" id="planTitle" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent">
                    </div>

                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Описание *</label>
                        <textarea name="description" id="planDescription" rows="3" required
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"></textarea>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Категория *</label>
                        <select name="category" id="planCategory" required
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent">
                            <option value="">Выберите категорию</option>
                            <option value="solar">Солнечная энергия</option>
                            <option value="wind">Ветровая энергия</option>
                            <option value="hydro">Гидроэнергия</option>
                            <option value="geothermal">Геотермальная энергия</option>
                            <option value="biomass">Биомасса</option>
                            <option value="hybrid">Гибридная энергия</option>
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Иконка</label>
                        <select name="icon_class" id="planIcon"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent">
                            <option value="fas fa-leaf">🌿 Лист</option>
                            <option value="fas fa-sun">☀️ Солнце</option>
                            <option value="fas fa-wind">💨 Ветер</option>
                            <option value="fas fa-water">💧 Вода</option>
                            <option value="fas fa-fire">🔥 Огонь</option>
                            <option value="fas fa-bolt">⚡ Молния</option>
                            <option value="fas fa-seedling">🌱 Росток</option>
                            <option value="fas fa-tree">🌳 Дерево</option>
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Минимальная сумма ($) *</label>
                        <input type="number" name="min_amount" id="planMinAmount" min="1" step="1" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Максимальная сумма ($)</label>
                        <input type="number" name="max_amount" id="planMaxAmount" min="1" step="1"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Дневная ставка (%) *</label>
                        <input type="number" name="daily_rate" id="planDailyRate" min="0.01" step="0.01" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Длительность (дни)</label>
                        <input type="number" name="duration_days" id="planDuration" min="1" step="1"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent">
                    </div>

                    <div class="md:col-span-2">
                        <label class="flex items-center">
                            <input type="checkbox" name="is_active" id="planActive" class="rounded border-gray-300 text-green-600 shadow-sm focus:border-green-300 focus:ring focus:ring-green-200 focus:ring-opacity-50">
                            <span class="ml-2 text-sm text-gray-700">Активный план</span>
                        </label>
                    </div>
                </div>

                <div class="flex space-x-3 mt-6">
                    <button type="button" onclick="closePlanModal()"
                            class="flex-1 border border-gray-300 text-gray-700 py-3 rounded-lg hover:bg-gray-50 transition-colors font-medium">
                        Отмена
                    </button>
                    <button type="submit" class="flex-1 bg-gradient-to-r from-green-500 to-teal-600 hover:from-green-600 hover:to-teal-700 text-white py-3 rounded-lg font-medium transition-all duration-300 shadow-lg">
                        Сохранить
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function openAddModal() {
    document.getElementById('modalTitle').textContent = 'Добавить майнинг-план';
    document.getElementById('formAction').value = 'add';
    document.getElementById('planForm').reset();
    document.getElementById('planModal').classList.remove('hidden');
}

function editPlan(plan) {
    document.getElementById('modalTitle').textContent = 'Редактировать майнинг-план';
    document.getElementById('formAction').value = 'edit';
    document.getElementById('planId').value = plan.id;
    document.getElementById('planTitle').value = plan.title;
    document.getElementById('planDescription').value = plan.description;
    document.getElementById('planCategory').value = plan.category;
    document.getElementById('planIcon').value = plan.icon_class;
    document.getElementById('planMinAmount').value = plan.min_amount;
    document.getElementById('planMaxAmount').value = plan.max_amount;
    document.getElementById('planDailyRate').value = plan.daily_rate;
    document.getElementById('planDuration').value = plan.duration_days;
    document.getElementById('planActive').checked = plan.is_active == 1;
    document.getElementById('planModal').classList.remove('hidden');
}

function closePlanModal() {
    document.getElementById('planModal').classList.add('hidden');
}

// Close modal when clicking outside
document.getElementById('planModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closePlanModal();
    }
});
</script>

<?php include '../includes/footer.php'; ?>
