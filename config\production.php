<?php
/**
 * AstroGenix Production Configuration
 * Optimized settings for production environment
 */

// Production Environment Settings
define('ENVIRONMENT', 'production');
define('DEBUG_MODE', false);

// Error Reporting (disabled for production)
error_reporting(0);
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../logs/error.log');

// Database Configuration (Update with your production values)
define('DB_HOST', 'localhost');
define('DB_NAME', 'astrogenix_prod');
define('DB_USER', 'astrogenix_user');
define('DB_PASS', 'your_secure_password_here');
define('DB_CHARSET', 'utf8mb4');

// Site Configuration
define('SITE_NAME', 'AstroGenix');
define('SITE_URL', 'https://your-domain.com'); // Update with your domain
define('SITE_EMAIL', '<EMAIL>'); // Update with your email
define('ADMIN_EMAIL', '<EMAIL>'); // Update with admin email

// Security Settings
define('SESSION_LIFETIME', 3600); // 1 hour
define('PASSWORD_MIN_LENGTH', 8);
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 minutes

// File Upload Settings
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_FILE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'pdf']);
define('UPLOAD_PATH', __DIR__ . '/../uploads/');

// Cache Settings
define('CACHE_ENABLED', true);
define('CACHE_LIFETIME', 3600); // 1 hour

// Rate Limiting
define('RATE_LIMIT_ENABLED', true);
define('RATE_LIMIT_REQUESTS', 100); // requests per hour
define('RATE_LIMIT_WINDOW', 3600); // 1 hour

// SSL/HTTPS Settings
define('FORCE_HTTPS', true);
define('SECURE_COOKIES', true);

// Performance Settings
define('GZIP_COMPRESSION', true);
define('MINIFY_CSS', true);
define('MINIFY_JS', true);

// Logging Settings
define('LOG_LEVEL', 'ERROR'); // ERROR, WARNING, INFO, DEBUG
define('LOG_FILE', __DIR__ . '/../logs/app.log');
define('LOG_MAX_SIZE', 10 * 1024 * 1024); // 10MB

// Email Settings (Update with your SMTP settings)
define('SMTP_HOST', 'smtp.your-provider.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', 'your-smtp-username');
define('SMTP_PASSWORD', 'your-smtp-password');
define('SMTP_ENCRYPTION', 'tls');

// API Settings
define('API_RATE_LIMIT', 1000); // requests per hour
define('API_KEY_LENGTH', 32);

// Mining Settings
define('MIN_INVESTMENT_AMOUNT', 10); // USDT
define('MAX_INVESTMENT_AMOUNT', 100000); // USDT
define('DAILY_PROFIT_CALCULATION_TIME', '00:00:00'); // UTC time

// Referral Settings
define('REFERRAL_LEVELS', 3);
define('REFERRAL_COMMISSION_L1', 10); // 10%
define('REFERRAL_COMMISSION_L2', 5);  // 5%
define('REFERRAL_COMMISSION_L3', 2);  // 2%

// Task Settings
define('DAILY_TASK_RESET_TIME', '00:00:00'); // UTC time
define('MAX_TASK_COMPLETIONS_PER_DAY', 10);

// Backup Settings
define('AUTO_BACKUP_ENABLED', true);
define('BACKUP_FREQUENCY', 'daily'); // daily, weekly, monthly
define('BACKUP_RETENTION_DAYS', 30);
define('BACKUP_PATH', __DIR__ . '/../backups/');

// Monitoring Settings
define('HEALTH_CHECK_ENABLED', true);
define('PERFORMANCE_MONITORING', true);
define('UPTIME_MONITORING', true);

// CDN Settings (if using CDN)
define('CDN_ENABLED', false);
define('CDN_URL', 'https://cdn.your-domain.com');

// Social Media Links
define('FACEBOOK_URL', 'https://facebook.com/astrogenix');
define('TWITTER_URL', 'https://twitter.com/astrogenix');
define('INSTAGRAM_URL', 'https://instagram.com/astrogenix');
define('LINKEDIN_URL', 'https://linkedin.com/company/astrogenix');

// Production Security Headers
function setSecurityHeaders() {
    if (!headers_sent()) {
        header('X-Content-Type-Options: nosniff');
        header('X-Frame-Options: DENY');
        header('X-XSS-Protection: 1; mode=block');
        header('Referrer-Policy: strict-origin-when-cross-origin');
        header('Permissions-Policy: geolocation=(), microphone=(), camera=()');
        
        if (FORCE_HTTPS) {
            header('Strict-Transport-Security: max-age=31536000; includeSubDomains; preload');
        }
    }
}

// Force HTTPS in production
function forceHTTPS() {
    if (FORCE_HTTPS && !isset($_SERVER['HTTPS']) && $_SERVER['SERVER_PORT'] != 443) {
        $redirectURL = 'https://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
        header("Location: $redirectURL");
        exit();
    }
}

// Initialize production settings
function initProduction() {
    // Set timezone
    date_default_timezone_set('UTC');
    
    // Set session settings
    ini_set('session.cookie_httponly', 1);
    ini_set('session.cookie_secure', SECURE_COOKIES ? 1 : 0);
    ini_set('session.use_strict_mode', 1);
    ini_set('session.gc_maxlifetime', SESSION_LIFETIME);
    
    // Set security headers
    setSecurityHeaders();
    
    // Force HTTPS
    forceHTTPS();
    
    // Enable output compression
    if (GZIP_COMPRESSION && !ob_get_level()) {
        ob_start('ob_gzhandler');
    }
}

// Database connection with production settings
function getProductionDBConnection() {
    static $pdo = null;
    
    if ($pdo === null) {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::ATTR_PERSISTENT => true,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . DB_CHARSET
            ];
            
            $pdo = new PDO($dsn, DB_USER, DB_PASS, $options);
        } catch (PDOException $e) {
            error_log("Database connection failed: " . $e->getMessage());
            
            // Show generic error in production
            http_response_code(503);
            die('Service temporarily unavailable. Please try again later.');
        }
    }
    
    return $pdo;
}

// Logging function for production
function logMessage($level, $message, $context = []) {
    if (!in_array($level, ['ERROR', 'WARNING', 'INFO', 'DEBUG'])) {
        return;
    }
    
    // Only log based on configured level
    $levels = ['ERROR' => 0, 'WARNING' => 1, 'INFO' => 2, 'DEBUG' => 3];
    $currentLevel = $levels[LOG_LEVEL] ?? 0;
    
    if ($levels[$level] > $currentLevel) {
        return;
    }
    
    $timestamp = date('Y-m-d H:i:s');
    $contextStr = !empty($context) ? ' ' . json_encode($context) : '';
    $logEntry = "[$timestamp] [$level] $message$contextStr" . PHP_EOL;
    
    // Rotate log if too large
    if (file_exists(LOG_FILE) && filesize(LOG_FILE) > LOG_MAX_SIZE) {
        rename(LOG_FILE, LOG_FILE . '.' . date('Y-m-d-H-i-s'));
    }
    
    file_put_contents(LOG_FILE, $logEntry, FILE_APPEND | LOCK_EX);
}

// Health check endpoint
function healthCheck() {
    $health = [
        'status' => 'healthy',
        'timestamp' => date('c'),
        'version' => '1.0.0',
        'checks' => []
    ];
    
    // Database check
    try {
        $pdo = getProductionDBConnection();
        $pdo->query('SELECT 1');
        $health['checks']['database'] = 'healthy';
    } catch (Exception $e) {
        $health['checks']['database'] = 'unhealthy';
        $health['status'] = 'unhealthy';
    }
    
    // Disk space check
    $freeSpace = disk_free_space(__DIR__);
    $totalSpace = disk_total_space(__DIR__);
    $usagePercent = (($totalSpace - $freeSpace) / $totalSpace) * 100;
    
    if ($usagePercent > 90) {
        $health['checks']['disk_space'] = 'warning';
        if ($health['status'] === 'healthy') {
            $health['status'] = 'warning';
        }
    } else {
        $health['checks']['disk_space'] = 'healthy';
    }
    
    return $health;
}

// Initialize production environment
if (ENVIRONMENT === 'production') {
    initProduction();
}

// Create necessary directories
$directories = [
    __DIR__ . '/../logs',
    __DIR__ . '/../backups',
    __DIR__ . '/../uploads',
    __DIR__ . '/../cache'
];

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}

// Create .htaccess for security
$htaccessContent = "
# AstroGenix Security Rules
Options -Indexes
ServerSignature Off

# Protect sensitive files
<Files ~ \"^(config|includes|logs|backups).*\">
    Order allow,deny
    Deny from all
</Files>

# Force HTTPS
RewriteEngine On
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection \"1; mode=block\"
";

if (!file_exists(__DIR__ . '/../.htaccess')) {
    file_put_contents(__DIR__ . '/../.htaccess', $htaccessContent);
}
?>
