# 🌱 AstroGenix - Мастер установки

## Добро пожаловать в установщик AstroGenix!

Этот автоматический мастер установки поможет вам быстро развернуть платформу экологического майнинга AstroGenix на вашем сервере.

## 🚀 Быстрый старт

### 1. Загрузите файлы
Убедитесь, что все файлы AstroGenix загружены на ваш веб-сервер.

### 2. Запустите установщик
Откройте браузер и перейдите по адресу:
```
http://ваш-домен.com/install/
```

### 3. Следуйте инструкциям
Мастер установки проведет вас через 6 простых шагов:

1. **Добро пожаловать** - Обзор платформы и функций
2. **Системные требования** - Автоматическая проверка сервера
3. **База данных** - Настройка подключения к БД
4. **Администратор** - Создание учетной записи админа
5. **Установка** - Автоматическая установка файлов и БД
6. **Завершение** - Финальные инструкции и ссылки

## 📋 Что проверяет установщик

### Системные требования
- ✅ PHP версия 7.4+
- ✅ Расширения PHP (PDO, MySQL, mbstring, OpenSSL, JSON, cURL)
- ✅ Права доступа к файлам и папкам
- ✅ Лимиты памяти и времени выполнения
- ✅ Настройки безопасности

### База данных
- ✅ Подключение к MySQL/MariaDB
- ✅ Права пользователя БД
- ✅ Создание таблиц и индексов
- ✅ Импорт демо-данных
- ✅ Проверка целостности данных

### Конфигурация
- ✅ Создание файла конфигурации
- ✅ Генерация ключей безопасности
- ✅ Настройка прав доступа
- ✅ Создание администратора
- ✅ Инициализация системных настроек

## 🔧 Что устанавливается

### База данных (11 таблиц)
- **users** - Пользователи системы
- **mining_plans** - Планы майнинга (6 эко-планов)
- **user_investments** - Инвестиции пользователей
- **referral_earnings** - Реферальные доходы
- **tasks** - Система заданий (8 базовых заданий)
- **user_task_completions** - Выполненные задания
- **activities** - Лог активности пользователей
- **transactions** - Финансовые транзакции
- **user_sessions** - Сессии пользователей
- **system_settings** - Системные настройки

### Демо-данные
- **Администратор**: admin / admin123
- **5 демо-пользователей** с паролем password123
- **6 майнинг-планов**: Solar, Wind, Hydro, Geothermal, Biomass, Hybrid
- **8 базовых заданий** с USDT наградами
- **Примеры транзакций** и реферальных связей
- **Системные настройки** по умолчанию

### Конфигурационные файлы
- **config/config.php** - Основная конфигурация
- **Ключи безопасности** - Автоматическая генерация
- **Настройки БД** - Подключение к базе данных
- **URL сайта** - Автоматическое определение
- **Email настройки** - Конфигурация SMTP

## 🔐 Безопасность

### Автоматические настройки безопасности
- Генерация уникальных ключей шифрования
- Настройка безопасных заголовков HTTP
- Конфигурация защищенных сессий
- Установка правильных прав доступа к файлам
- Создание защищенных паролей администратора

### Рекомендации после установки
1. **Удалите папку install/** после завершения установки
2. **Измените пароль администратора** на более сложный
3. **Настройте SSL-сертификат** для HTTPS
4. **Обновите email настройки** в админ-панели
5. **Проверьте права доступа** к файлам и папкам

## 📱 Мобильная оптимизация

Установщик полностью адаптирован для мобильных устройств:
- Responsive дизайн для всех экранов
- Touch-friendly интерфейс
- Оптимизированные формы для мобильных
- Прогресс-индикатор установки
- Удобная навигация между шагами

## 🎨 Дизайн

Установщик использует фирменный стиль AstroGenix:
- Зелено-фиолетовая цветовая схема
- Экологические иконки и анимации
- Современные градиенты и тени
- Плавные переходы и эффекты
- Профессиональный внешний вид

## ⚠️ Устранение неполадок

### Частые проблемы

**1. Ошибка "Не удается подключиться к базе данных"**
- Проверьте правильность данных подключения
- Убедитесь, что MySQL сервер запущен
- Проверьте права пользователя БД

**2. Ошибка "Недостаточно прав доступа"**
```bash
chmod 755 config/ logs/ uploads/ cache/
chown www-data:www-data config/ logs/ uploads/ cache/
```

**3. Ошибка "PHP расширение не найдено"**
- Установите недостающие расширения PHP
- Перезапустите веб-сервер
- Проверьте конфигурацию PHP

**4. Ошибка "Превышен лимит времени выполнения"**
- Увеличьте max_execution_time в PHP
- Увеличьте memory_limit
- Проверьте скорость подключения к БД

### Логи установки
Все действия установщика записываются в логи:
- Проверка системных требований
- Подключение к базе данных
- Создание таблиц и данных
- Настройка конфигурации
- Ошибки и предупреждения

## 📞 Поддержка

### Документация
- **Полное руководство**: `/INSTALLATION-GUIDE.md`
- **Конфигурация**: `/config/config.example.php`
- **Production гид**: `/PRODUCTION-README.md`

### Тестирование
После установки запустите системный тест:
```
http://ваш-домен.com/test/system-test.php
```

### Контакты
- **GitHub**: [ссылка на репозиторий]
- **Документация**: Полная документация в корне проекта
- **Техподдержка**: Создайте issue в GitHub

## 🎯 Следующие шаги

После успешной установки:

1. **Войдите в админ-панель**: `/admin/`
2. **Обновите настройки сайта**
3. **Настройте SMTP для email**
4. **Добавьте реальные майнинг-планы**
5. **Настройте платежные системы**
6. **Протестируйте все функции**
7. **Запустите в production**

## 🌟 Особенности AstroGenix

### Экологическая тематика
- 6 типов эко-майнинга
- Зеленые технологии
- Устойчивое развитие
- Экологические анимации

### Современные технологии
- PHP 8.0+ совместимость
- MySQL 8.0 оптимизация
- Responsive дизайн
- Progressive Web App готовность

### Полная функциональность
- Инвестиционные планы
- Реферальная система
- Система заданий
- Мобильная оптимизация
- Административная панель
- Система безопасности

---

**Добро пожаловать в AstroGenix!** 🌱⚡

Начните установку прямо сейчас и создайте свою платформу экологического майнинга за несколько минут!
