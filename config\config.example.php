<?php
/**
 * AstroGenix Configuration File Example
 * Copy this file to config.php and update with your settings
 */

// Database Configuration
define('DB_HOST', 'localhost');
define('DB_PORT', '3306');
define('DB_NAME', 'astrogenix');
define('DB_USER', 'your_db_user');
define('DB_PASS', 'your_db_password');
define('DB_PREFIX', ''); // Optional table prefix

// Site Configuration
define('SITE_URL', 'https://your-domain.com');
define('SITE_NAME', 'AstroGenix');
define('SITE_DESCRIPTION', 'Eco-Friendly Cryptocurrency Mining Platform');
define('ADMIN_EMAIL', '<EMAIL>');
define('SUPPORT_EMAIL', '<EMAIL>');

// Security Configuration
define('SECRET_KEY', 'your-secret-key-64-characters-long-random-string-here-change-this');
define('ENCRYPTION_KEY', 'your-encryption-key-32-chars-here');
define('SESSION_LIFETIME', 3600); // 1 hour
define('PASSWORD_SALT', 'your-password-salt-here');

// Environment Configuration
define('ENVIRONMENT', 'production'); // 'development' or 'production'
define('DEBUG_MODE', false); // Set to true for development
define('MAINTENANCE_MODE', false); // Set to true to enable maintenance mode

// Email Configuration (SMTP)
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password');
define('SMTP_ENCRYPTION', 'tls'); // 'tls' or 'ssl'
define('SMTP_FROM_EMAIL', '<EMAIL>');
define('SMTP_FROM_NAME', 'AstroGenix');

// File Upload Configuration
define('UPLOAD_MAX_SIZE', 10485760); // 10MB in bytes
define('ALLOWED_EXTENSIONS', 'jpg,jpeg,png,gif,pdf,doc,docx');
define('UPLOAD_PATH', 'uploads/');

// Security Settings
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 minutes
define('SESSION_REGENERATE_TIME', 300); // 5 minutes
define('CSRF_TOKEN_LIFETIME', 3600); // 1 hour

// Investment Settings
define('MIN_INVESTMENT', 100.00);
define('MAX_INVESTMENT', 100000.00);
define('DEFAULT_CURRENCY', 'USDT');

// Referral Settings
define('REFERRAL_LEVEL_1_COMMISSION', 10.00); // 10%
define('REFERRAL_LEVEL_2_COMMISSION', 5.00);  // 5%
define('REFERRAL_LEVEL_3_COMMISSION', 2.00);  // 2%
define('MAX_REFERRAL_LEVELS', 3);

// Task System Settings
define('DAILY_LOGIN_REWARD', 0.50);
define('TASK_COMPLETION_TIMEOUT', 86400); // 24 hours
define('MAX_TASK_COMPLETIONS_PER_DAY', 10);

// Withdrawal Settings
define('MIN_WITHDRAWAL', 10.00);
define('WITHDRAWAL_FEE_PERCENTAGE', 2.00); // 2%
define('WITHDRAWAL_PROCESSING_TIME', 24); // hours

// API Settings
define('API_RATE_LIMIT', 100); // requests per hour
define('API_KEY_LENGTH', 32);
define('API_VERSION', 'v1');

// Social Media Links
define('FACEBOOK_URL', 'https://facebook.com/astrogenix');
define('TWITTER_URL', 'https://twitter.com/astrogenix');
define('INSTAGRAM_URL', 'https://instagram.com/astrogenix');
define('LINKEDIN_URL', 'https://linkedin.com/company/astrogenix');

// Cache Settings
define('CACHE_ENABLED', true);
define('CACHE_LIFETIME', 3600); // 1 hour
define('CACHE_PATH', 'cache/');

// Logging Settings
define('LOG_ENABLED', true);
define('LOG_LEVEL', 'INFO'); // DEBUG, INFO, WARNING, ERROR
define('LOG_PATH', 'logs/');
define('LOG_MAX_SIZE', 10485760); // 10MB
define('LOG_MAX_FILES', 10);

// Timezone
date_default_timezone_set('UTC');

// Database Connection Function
function getDBConnection() {
    static $pdo = null;
    
    if ($pdo === null) {
        try {
            $dsn = 'mysql:host=' . DB_HOST . ';port=' . DB_PORT . ';dbname=' . DB_NAME . ';charset=utf8mb4';
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::ATTR_PERSISTENT => false
            ];
            
            $pdo = new PDO($dsn, DB_USER, DB_PASS, $options);
            
            // Set SQL mode for better compatibility
            $pdo->exec("SET sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO'");
            
        } catch (PDOException $e) {
            if (DEBUG_MODE) {
                die('Database connection failed: ' . $e->getMessage());
            } else {
                die('Database connection failed. Please contact support.');
            }
        }
    }
    
    return $pdo;
}

// Error Reporting
if (ENVIRONMENT === 'development' || DEBUG_MODE) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
    ini_set('log_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
    ini_set('log_errors', 1);
}

// Set error log file
ini_set('error_log', LOG_PATH . 'php_errors.log');

// Session Configuration
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_secure', 1);
ini_set('session.use_strict_mode', 1);
ini_set('session.cookie_samesite', 'Strict');

// Security Headers Function
function setSecurityHeaders() {
    if (!headers_sent()) {
        header('X-Content-Type-Options: nosniff');
        header('X-Frame-Options: DENY');
        header('X-XSS-Protection: 1; mode=block');
        header('Referrer-Policy: strict-origin-when-cross-origin');
        
        if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
            header('Strict-Transport-Security: max-age=31536000; includeSubDomains; preload');
        }
        
        // Content Security Policy
        $csp = "default-src 'self'; ";
        $csp .= "script-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com; ";
        $csp .= "style-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com; ";
        $csp .= "img-src 'self' data: https:; ";
        $csp .= "font-src 'self' https://cdnjs.cloudflare.com; ";
        $csp .= "connect-src 'self'; ";
        $csp .= "frame-ancestors 'none';";
        
        header("Content-Security-Policy: $csp");
    }
}

// Utility Functions
function sanitizeInput($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token']) || 
        !isset($_SESSION['csrf_token_time']) || 
        (time() - $_SESSION['csrf_token_time']) > CSRF_TOKEN_LIFETIME) {
        
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        $_SESSION['csrf_token_time'] = time();
    }
    
    return $_SESSION['csrf_token'];
}

function validateCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && 
           hash_equals($_SESSION['csrf_token'], $token) &&
           isset($_SESSION['csrf_token_time']) &&
           (time() - $_SESSION['csrf_token_time']) <= CSRF_TOKEN_LIFETIME;
}

function logActivity($message, $level = 'INFO', $context = []) {
    if (!LOG_ENABLED) return;
    
    $logFile = LOG_PATH . 'activity_' . date('Y-m-d') . '.log';
    $timestamp = date('Y-m-d H:i:s');
    $contextStr = !empty($context) ? ' | Context: ' . json_encode($context) : '';
    $logEntry = "[$timestamp] [$level] $message$contextStr" . PHP_EOL;
    
    // Create logs directory if it doesn't exist
    if (!is_dir(LOG_PATH)) {
        mkdir(LOG_PATH, 0755, true);
    }
    
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
    
    // Rotate logs if file is too large
    if (file_exists($logFile) && filesize($logFile) > LOG_MAX_SIZE) {
        $rotatedFile = LOG_PATH . 'activity_' . date('Y-m-d') . '_' . time() . '.log';
        rename($logFile, $rotatedFile);
        
        // Clean old log files
        $logFiles = glob(LOG_PATH . 'activity_*.log');
        if (count($logFiles) > LOG_MAX_FILES) {
            usort($logFiles, function($a, $b) {
                return filemtime($a) - filemtime($b);
            });
            
            $filesToDelete = array_slice($logFiles, 0, count($logFiles) - LOG_MAX_FILES);
            foreach ($filesToDelete as $file) {
                unlink($file);
            }
        }
    }
}

function formatCurrency($amount, $currency = null) {
    if ($currency === null) {
        $currency = DEFAULT_CURRENCY;
    }
    
    return number_format($amount, 2) . ' ' . $currency;
}

function isMaintenanceMode() {
    return MAINTENANCE_MODE;
}

function redirectToHTTPS() {
    if (!isset($_SERVER['HTTPS']) || $_SERVER['HTTPS'] !== 'on') {
        $redirectURL = 'https://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
        header("Location: $redirectURL", true, 301);
        exit();
    }
}

// Auto-redirect to HTTPS in production
if (ENVIRONMENT === 'production') {
    redirectToHTTPS();
}

// Set security headers
setSecurityHeaders();

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Regenerate session ID periodically
if (!isset($_SESSION['last_regeneration']) || 
    (time() - $_SESSION['last_regeneration']) > SESSION_REGENERATE_TIME) {
    session_regenerate_id(true);
    $_SESSION['last_regeneration'] = time();
}
?>
