<?php
require_once '../config/config.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

requireLogin();

$page_title = 'Задания и миссии';
$user_id = getCurrentUserId();
$user = getUserById($user_id);

// Get available tasks
$db = getDB();

// Get all active tasks with completion status
$stmt = $db->prepare("
    SELECT t.*, 
           utc.completed_at,
           utc.times_completed,
           CASE 
               WHEN utc.user_id IS NOT NULL THEN 1 
               ELSE 0 
           END as is_completed,
           CASE 
               WHEN t.max_completions > 1 THEN COALESCE(utc.times_completed, 0)
               ELSE 0
           END as completion_count
    FROM tasks t
    LEFT JOIN user_task_completions utc ON t.id = utc.task_id AND utc.user_id = ?
    WHERE t.is_active = 1 
    AND (t.start_date IS NULL OR t.start_date <= CURDATE())
    AND (t.end_date IS NULL OR t.end_date >= CURDATE())
    ORDER BY 
        CASE t.task_type 
            WHEN 'daily' THEN 1 
            WHEN 'weekly' THEN 2 
            WHEN 'monthly' THEN 3 
            WHEN 'referral' THEN 4 
            WHEN 'investment' THEN 5 
            WHEN 'one_time' THEN 6 
        END,
        t.reward_amount DESC
");
$stmt->execute([$user_id]);
$tasks = $stmt->fetchAll();

// Get user's task completion statistics
$stmt = $db->prepare("
    SELECT 
        COUNT(DISTINCT task_id) as total_completed,
        SUM(CASE WHEN t.reward_type = 'usdt' THEN t.reward_amount ELSE 0 END) as total_usdt_earned,
        SUM(CASE WHEN t.reward_type = 'mining_power' THEN t.reward_amount ELSE 0 END) as total_mining_power_earned,
        SUM(CASE WHEN t.reward_type = 'eco_score' THEN t.reward_amount ELSE 0 END) as total_eco_score_earned
    FROM user_task_completions utc
    JOIN tasks t ON utc.task_id = t.id
    WHERE utc.user_id = ?
");
$stmt->execute([$user_id]);
$task_stats = $stmt->fetch();

// Group tasks by type
$grouped_tasks = [];
foreach ($tasks as $task) {
    $grouped_tasks[$task['task_type']][] = $task;
}

include '../includes/header.php';
?>

<div class="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">Задания и миссии</h1>
            <p class="text-gray-600">Выполняйте задания и получайте USDT награды</p>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-8">
            <div class="bg-white rounded-xl p-6 card-shadow border border-gray-100 group hover:shadow-xl transition-all duration-300">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-teal-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <i class="fas fa-tasks text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Выполнено заданий</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo number_format($task_stats['total_completed'] ?? 0); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 card-shadow border border-gray-100 group hover:shadow-xl transition-all duration-300">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <i class="fas fa-coins text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">USDT заработано</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo number_format($task_stats['total_usdt_earned'] ?? 0, 2); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 card-shadow border border-gray-100 group hover:shadow-xl transition-all duration-300">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <i class="fas fa-bolt text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Майнинг сила</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo number_format($task_stats['total_mining_power_earned'] ?? 0, 2); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 card-shadow border border-gray-100 group hover:shadow-xl transition-all duration-300">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <i class="fas fa-leaf text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Эко-счет</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo number_format($task_stats['total_eco_score_earned'] ?? 0); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Task Categories -->
        <?php 
        $task_type_info = [
            'daily' => [
                'title' => 'Ежедневные задания',
                'description' => 'Выполняйте каждый день для получения постоянных наград',
                'icon' => 'fas fa-calendar-day',
                'color' => 'from-blue-500 to-cyan-600'
            ],
            'weekly' => [
                'title' => 'Еженедельные задания',
                'description' => 'Более сложные задания с большими наградами',
                'icon' => 'fas fa-calendar-week',
                'color' => 'from-green-500 to-teal-600'
            ],
            'monthly' => [
                'title' => 'Месячные задания',
                'description' => 'Долгосрочные цели с максимальными наградами',
                'icon' => 'fas fa-calendar-alt',
                'color' => 'from-purple-500 to-pink-600'
            ],
            'referral' => [
                'title' => 'Реферальные задания',
                'description' => 'Приглашайте друзей и получайте бонусы',
                'icon' => 'fas fa-users',
                'color' => 'from-orange-500 to-red-600'
            ],
            'investment' => [
                'title' => 'Инвестиционные задания',
                'description' => 'Задания связанные с эко-майнингом',
                'icon' => 'fas fa-seedling',
                'color' => 'from-green-600 to-emerald-600'
            ],
            'one_time' => [
                'title' => 'Разовые задания',
                'description' => 'Специальные задания для новых пользователей',
                'icon' => 'fas fa-star',
                'color' => 'from-yellow-500 to-orange-500'
            ]
        ];

        foreach ($grouped_tasks as $task_type => $type_tasks): 
            if (empty($type_tasks)) continue;
            $type_info = $task_type_info[$task_type];
        ?>
            <div class="bg-white rounded-xl card-shadow border border-gray-100 mb-8">
                <div class="p-6 border-b border-gray-200">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gradient-to-br <?php echo $type_info['color']; ?> rounded-lg flex items-center justify-center mr-4 shadow-lg">
                            <i class="<?php echo $type_info['icon']; ?> text-white text-xl"></i>
                        </div>
                        <div>
                            <h2 class="text-xl font-bold text-gray-900"><?php echo $type_info['title']; ?></h2>
                            <p class="text-gray-600 mt-1"><?php echo $type_info['description']; ?></p>
                        </div>
                    </div>
                </div>
                <div class="p-4 md:p-6">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6 task-categories">
                        <?php foreach ($type_tasks as $task): ?>
                            <div class="border border-gray-200 rounded-lg p-4 md:p-6 hover:shadow-md transition-shadow duration-300 task-card <?php echo $task['is_completed'] ? 'bg-green-50 border-green-200' : 'bg-white'; ?>">
                                <div class="flex justify-between items-start mb-4">
                                    <div class="flex-1">
                                        <h3 class="font-semibold text-gray-900 mb-2"><?php echo htmlspecialchars($task['title']); ?></h3>
                                        <p class="text-sm text-gray-600 mb-3"><?php echo htmlspecialchars($task['description']); ?></p>
                                        
                                        <!-- Reward Display -->
                                        <div class="flex flex-col sm:flex-row sm:items-center sm:space-x-4 space-y-2 sm:space-y-0 mb-3 task-reward-display">
                                            <div class="flex items-center">
                                                <i class="fas <?php 
                                                    echo $task['reward_type'] === 'usdt' ? 'fa-coins text-yellow-500' : 
                                                        ($task['reward_type'] === 'mining_power' ? 'fa-bolt text-purple-500' : 
                                                        ($task['reward_type'] === 'eco_score' ? 'fa-leaf text-green-500' : 'fa-star text-blue-500')); 
                                                ?> mr-2"></i>
                                                <span class="font-semibold text-gray-900">
                                                    <?php echo number_format($task['reward_amount'], 2); ?>
                                                    <?php echo strtoupper(str_replace('_', ' ', $task['reward_type'])); ?>
                                                </span>
                                            </div>
                                            
                                            <?php if ($task['max_completions'] > 1): ?>
                                                <div class="text-sm text-gray-500">
                                                    <?php echo $task['completion_count']; ?>/<?php echo $task['max_completions']; ?> выполнений
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>

                                <!-- Action Button -->
                                <div class="flex justify-between items-center">
                                    <?php if ($task['is_completed'] && $task['max_completions'] == 1): ?>
                                        <span class="bg-green-100 text-green-800 px-4 py-2 rounded-lg font-medium flex items-center">
                                            <i class="fas fa-check mr-2"></i>Выполнено
                                        </span>
                                        <span class="text-sm text-gray-500">
                                            <?php echo date('d.m.Y', strtotime($task['completed_at'])); ?>
                                        </span>
                                    <?php elseif ($task['completion_count'] >= $task['max_completions'] && $task['max_completions'] > 1): ?>
                                        <span class="bg-gray-100 text-gray-800 px-4 py-2 rounded-lg font-medium flex items-center">
                                            <i class="fas fa-check mr-2"></i>Лимит исчерпан
                                        </span>
                                    <?php else: ?>
                                        <button onclick="completeTask(<?php echo $task['id']; ?>)"
                                                class="bg-gradient-to-r from-green-500 to-teal-600 hover:from-green-600 hover:to-teal-700 text-white px-4 md:px-6 py-2 rounded-lg font-medium transition-all duration-300 transform hover:scale-105 shadow-lg flex items-center justify-center touch-target task-action-button">
                                            <i class="fas fa-play mr-2"></i>Выполнить
                                        </button>
                                        
                                        <?php if ($task['task_type'] === 'daily' && $task['is_completed']): ?>
                                            <span class="text-sm text-gray-500">Доступно завтра</span>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>

        <?php if (empty($tasks)): ?>
            <div class="bg-white rounded-xl card-shadow border border-gray-100">
                <div class="p-12 text-center">
                    <i class="fas fa-tasks text-gray-400 text-6xl mb-6"></i>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">Нет доступных заданий</h3>
                    <p class="text-gray-600">Новые задания появятся в ближайшее время</p>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
async function completeTask(taskId) {
    try {
        const response = await fetch('../api/complete-task.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                task_id: taskId
            })
        });

        const result = await response.json();

        if (result.success) {
            showNotification(result.message, 'success');

            // Update the UI
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else {
            showNotification(result.message || 'Ошибка при выполнении задания', 'error');
        }
    } catch (error) {
        console.error('Error completing task:', error);
        showNotification('Произошла ошибка при выполнении задания', 'error');
    }
}

function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        'bg-blue-500 text-white'
    }`;
    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas ${type === 'success' ? 'fa-check' : type === 'error' ? 'fa-times' : 'fa-info'} mr-2"></i>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    // Animate out and remove
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 4000);
}

// Add eco-mining animations
document.addEventListener('DOMContentLoaded', function() {
    // Animate cards on scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe all cards
    document.querySelectorAll('.card-shadow').forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(card);
    });

    // Add floating particles animation
    createFloatingParticles();
});

function createFloatingParticles() {
    const particleContainer = document.createElement('div');
    particleContainer.className = 'fixed inset-0 pointer-events-none z-0';
    document.body.appendChild(particleContainer);

    for (let i = 0; i < 20; i++) {
        const particle = document.createElement('div');
        particle.className = 'absolute w-2 h-2 bg-green-400 rounded-full opacity-20';
        particle.style.left = Math.random() * 100 + '%';
        particle.style.top = Math.random() * 100 + '%';
        particle.style.animation = `float ${3 + Math.random() * 4}s ease-in-out infinite`;
        particle.style.animationDelay = Math.random() * 2 + 's';
        particleContainer.appendChild(particle);
    }
}
</script>

<style>
@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.2;
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 0.5;
    }
}

.card-shadow {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.card-shadow:hover {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}
</style>

<?php include '../includes/footer.php'; ?>
