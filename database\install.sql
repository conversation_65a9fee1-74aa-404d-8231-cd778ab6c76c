-- AstroGenix Eco-Mining Platform Database Installation Script
-- Version: 1.0
-- Created: 2024-01-01

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET AUTOCOMMIT = 0;
START TRANSACTION;
SET time_zone = "+00:00";

-- --------------------------------------------------------
-- Database Structure for AstroGenix Platform
-- --------------------------------------------------------

-- Table structure for table `users`
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `first_name` varchar(50) DEFAULT NULL,
  `last_name` varchar(50) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `country` varchar(50) DEFAULT NULL,
  `balance` decimal(15,2) DEFAULT 0.00,
  `total_invested` decimal(15,2) DEFAULT 0.00,
  `total_earned` decimal(15,2) DEFAULT 0.00,
  `referral_code` varchar(20) UNIQUE DEFAULT NULL,
  `referred_by` int(11) DEFAULT NULL,
  `referral_earnings` decimal(15,2) DEFAULT 0.00,
  `role` enum('user','admin','moderator') DEFAULT 'user',
  `status` enum('active','inactive','suspended','pending') DEFAULT 'pending',
  `email_verified` tinyint(1) DEFAULT 0,
  `verification_token` varchar(100) DEFAULT NULL,
  `reset_token` varchar(100) DEFAULT NULL,
  `reset_expires` datetime DEFAULT NULL,
  `last_login` datetime DEFAULT NULL,
  `login_attempts` int(11) DEFAULT 0,
  `locked_until` datetime DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  UNIQUE KEY `referral_code` (`referral_code`),
  KEY `referred_by` (`referred_by`),
  KEY `status` (`status`),
  KEY `role` (`role`),
  CONSTRAINT `users_referred_by_fk` FOREIGN KEY (`referred_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table structure for table `mining_plans`
CREATE TABLE `mining_plans` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text,
  `category` enum('solar','wind','hydro','geothermal','biomass','hybrid') NOT NULL,
  `min_amount` decimal(15,2) NOT NULL,
  `max_amount` decimal(15,2) NOT NULL,
  `daily_rate` decimal(5,2) NOT NULL,
  `duration_days` int(11) NOT NULL,
  `total_return` decimal(5,2) NOT NULL,
  `risk_level` enum('low','medium','high') DEFAULT 'medium',
  `energy_type` varchar(50) DEFAULT NULL,
  `location` varchar(100) DEFAULT NULL,
  `capacity_mw` decimal(10,2) DEFAULT NULL,
  `co2_offset_tons` decimal(10,2) DEFAULT NULL,
  `icon_class` varchar(50) DEFAULT 'fas fa-leaf',
  `image_url` varchar(255) DEFAULT NULL,
  `features` json DEFAULT NULL,
  `status` enum('active','inactive','coming_soon','sold_out') DEFAULT 'active',
  `sort_order` int(11) DEFAULT 0,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `category` (`category`),
  KEY `status` (`status`),
  KEY `sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table structure for table `user_investments`
CREATE TABLE `user_investments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `mining_plan_id` int(11) NOT NULL,
  `amount` decimal(15,2) NOT NULL,
  `daily_profit` decimal(15,2) NOT NULL,
  `total_earned` decimal(15,2) DEFAULT 0.00,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `last_profit_date` date DEFAULT NULL,
  `status` enum('active','completed','cancelled','paused') DEFAULT 'active',
  `transaction_hash` varchar(100) DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `mining_plan_id` (`mining_plan_id`),
  KEY `status` (`status`),
  KEY `start_date` (`start_date`),
  KEY `end_date` (`end_date`),
  CONSTRAINT `user_investments_user_fk` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `user_investments_plan_fk` FOREIGN KEY (`mining_plan_id`) REFERENCES `mining_plans` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table structure for table `referral_earnings`
CREATE TABLE `referral_earnings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `referral_user_id` int(11) NOT NULL,
  `investment_id` int(11) NOT NULL,
  `level` tinyint(1) NOT NULL,
  `commission_rate` decimal(5,2) NOT NULL,
  `investment_amount` decimal(15,2) NOT NULL,
  `commission_amount` decimal(15,2) NOT NULL,
  `status` enum('pending','paid','cancelled') DEFAULT 'pending',
  `paid_at` datetime DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `referral_user_id` (`referral_user_id`),
  KEY `investment_id` (`investment_id`),
  KEY `level` (`level`),
  KEY `status` (`status`),
  CONSTRAINT `referral_earnings_user_fk` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `referral_earnings_referral_fk` FOREIGN KEY (`referral_user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `referral_earnings_investment_fk` FOREIGN KEY (`investment_id`) REFERENCES `user_investments` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table structure for table `tasks`
CREATE TABLE `tasks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(200) NOT NULL,
  `description` text,
  `task_type` enum('daily','weekly','monthly','one_time','referral','investment','social','login') NOT NULL,
  `category` enum('basic','social','investment','referral','bonus') DEFAULT 'basic',
  `reward_amount` decimal(15,2) NOT NULL,
  `reward_type` enum('usdt','points','bonus') DEFAULT 'usdt',
  `requirements` json DEFAULT NULL,
  `max_completions` int(11) DEFAULT 1,
  `reset_frequency` enum('none','daily','weekly','monthly') DEFAULT 'none',
  `icon_class` varchar(50) DEFAULT 'fas fa-tasks',
  `difficulty` enum('easy','medium','hard') DEFAULT 'easy',
  `status` enum('active','inactive','draft') DEFAULT 'active',
  `start_date` datetime DEFAULT NULL,
  `end_date` datetime DEFAULT NULL,
  `sort_order` int(11) DEFAULT 0,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `task_type` (`task_type`),
  KEY `category` (`category`),
  KEY `status` (`status`),
  KEY `sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table structure for table `user_task_completions`
CREATE TABLE `user_task_completions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `task_id` int(11) NOT NULL,
  `completed_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `reward_claimed` tinyint(1) DEFAULT 0,
  `reward_amount` decimal(15,2) DEFAULT 0.00,
  `claimed_at` datetime DEFAULT NULL,
  `verification_data` json DEFAULT NULL,
  `status` enum('completed','verified','rewarded','rejected') DEFAULT 'completed',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_task_daily` (`user_id`,`task_id`,`completed_at`),
  KEY `user_id` (`user_id`),
  KEY `task_id` (`task_id`),
  KEY `completed_at` (`completed_at`),
  KEY `status` (`status`),
  CONSTRAINT `user_task_completions_user_fk` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `user_task_completions_task_fk` FOREIGN KEY (`task_id`) REFERENCES `tasks` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table structure for table `activities`
CREATE TABLE `activities` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `type` enum('login','investment','withdrawal','referral','task_completion','profile_update','admin_action') NOT NULL,
  `description` text NOT NULL,
  `amount` decimal(15,2) DEFAULT NULL,
  `reference_id` int(11) DEFAULT NULL,
  `reference_type` varchar(50) DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `metadata` json DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `type` (`type`),
  KEY `created_at` (`created_at`),
  KEY `reference` (`reference_type`,`reference_id`),
  CONSTRAINT `activities_user_fk` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table structure for table `transactions`
CREATE TABLE `transactions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `type` enum('deposit','withdrawal','investment','profit','referral_commission','task_reward','bonus') NOT NULL,
  `amount` decimal(15,2) NOT NULL,
  `currency` varchar(10) DEFAULT 'USDT',
  `status` enum('pending','completed','failed','cancelled') DEFAULT 'pending',
  `description` text,
  `reference_id` int(11) DEFAULT NULL,
  `reference_type` varchar(50) DEFAULT NULL,
  `transaction_hash` varchar(100) DEFAULT NULL,
  `wallet_address` varchar(100) DEFAULT NULL,
  `fee_amount` decimal(15,2) DEFAULT 0.00,
  `processed_at` datetime DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `type` (`type`),
  KEY `status` (`status`),
  KEY `created_at` (`created_at`),
  KEY `reference` (`reference_type`,`reference_id`),
  CONSTRAINT `transactions_user_fk` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table structure for table `user_sessions`
CREATE TABLE `user_sessions` (
  `id` varchar(128) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `last_activity` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `data` text,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `last_activity` (`last_activity`),
  CONSTRAINT `user_sessions_user_fk` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table structure for table `system_settings`
CREATE TABLE `system_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text,
  `setting_type` enum('string','integer','decimal','boolean','json') DEFAULT 'string',
  `description` text,
  `category` varchar(50) DEFAULT 'general',
  `is_public` tinyint(1) DEFAULT 0,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `setting_key` (`setting_key`),
  KEY `category` (`category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------
-- Sample Data for AstroGenix Platform
-- --------------------------------------------------------

-- Insert default admin user (password: admin123)
INSERT INTO `users` (`id`, `username`, `email`, `password`, `first_name`, `last_name`, `balance`, `referral_code`, `role`, `status`, `email_verified`, `created_at`) VALUES
(1, 'admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Admin', 'User', 10000.00, 'ADMIN001', 'admin', 'active', 1, NOW());

-- Insert sample mining plans
INSERT INTO `mining_plans` (`name`, `description`, `category`, `min_amount`, `max_amount`, `daily_rate`, `duration_days`, `total_return`, `risk_level`, `energy_type`, `location`, `capacity_mw`, `co2_offset_tons`, `icon_class`, `features`, `status`, `sort_order`) VALUES
('Solar Genesis', 'High-efficiency solar mining farm with cutting-edge photovoltaic technology', 'solar', 100.00, 10000.00, 1.20, 365, 438.00, 'low', 'Solar Photovoltaic', 'Nevada, USA', 50.5, 25000.00, 'fas fa-sun', '["24/7 monitoring", "Weather protection", "Grid backup", "Smart inverters"]', 'active', 1),
('Wind Titan', 'Offshore wind turbine mining operation with consistent energy generation', 'wind', 250.00, 25000.00, 1.50, 300, 450.00, 'medium', 'Offshore Wind', 'North Sea, Denmark', 120.0, 60000.00, 'fas fa-wind', '["Offshore location", "High wind speeds", "Advanced turbines", "Marine protection"]', 'active', 2),
('Hydro Force', 'Run-of-river hydroelectric mining with minimal environmental impact', 'hydro', 500.00, 50000.00, 1.80, 270, 486.00, 'low', 'Run-of-River Hydro', 'British Columbia, Canada', 75.2, 40000.00, 'fas fa-water', '["Fish-friendly design", "Continuous flow", "Low maintenance", "Grid stability"]', 'active', 3),
('Geothermal Deep', 'Deep geothermal energy mining with 99.9% uptime guarantee', 'geothermal', 1000.00, 100000.00, 2.10, 240, 504.00, 'medium', 'Enhanced Geothermal', 'Iceland', 200.0, 100000.00, 'fas fa-fire', '["24/7 operation", "Weather independent", "Baseload power", "Volcanic energy"]', 'active', 4),
('Biomass Green', 'Sustainable biomass mining using agricultural waste and energy crops', 'biomass', 200.00, 20000.00, 1.35, 330, 445.50, 'medium', 'Agricultural Biomass', 'Iowa, USA', 35.8, 18000.00, 'fas fa-seedling', '["Carbon neutral", "Waste utilization", "Local sourcing", "Community support"]', 'active', 5),
('Hybrid Nexus', 'Multi-source renewable energy mining combining solar, wind, and storage', 'hybrid', 750.00, 75000.00, 1.95, 300, 585.00, 'low', 'Solar+Wind+Battery', 'California, USA', 150.0, 75000.00, 'fas fa-bolt', '["Energy storage", "Grid balancing", "Peak shaving", "Smart management"]', 'active', 6);

-- Insert sample tasks
INSERT INTO `tasks` (`title`, `description`, `task_type`, `category`, `reward_amount`, `requirements`, `icon_class`, `difficulty`, `status`, `sort_order`) VALUES
('Daily Login Bonus', 'Login to your AstroGenix account daily to earn rewards', 'daily', 'basic', 0.50, '{"action": "login", "frequency": "daily"}', 'fas fa-sign-in-alt', 'easy', 'active', 1),
('First Investment', 'Make your first eco-mining investment to unlock platform features', 'one_time', 'investment', 5.00, '{"action": "invest", "min_amount": 100}', 'fas fa-seedling', 'easy', 'active', 2),
('Refer a Friend', 'Invite friends to join the eco-mining revolution', 'referral', 'referral', 10.00, '{"action": "referral", "min_investment": 100}', 'fas fa-users', 'medium', 'active', 3),
('Weekly Investment Goal', 'Invest at least $500 in eco-mining plans this week', 'weekly', 'investment', 15.00, '{"action": "invest", "min_amount": 500, "period": "week"}', 'fas fa-chart-line', 'medium', 'active', 4),
('Social Media Share', 'Share AstroGenix on your social media platforms', 'daily', 'social', 1.00, '{"action": "social_share", "platforms": ["facebook", "twitter", "instagram"]}', 'fas fa-share-alt', 'easy', 'active', 5),
('Complete Profile', 'Fill out your complete profile information', 'one_time', 'basic', 3.00, '{"action": "profile_complete", "fields": ["phone", "country", "avatar"]}', 'fas fa-user-edit', 'easy', 'active', 6),
('Monthly Mining Master', 'Invest $2000+ in mining plans this month', 'monthly', 'investment', 50.00, '{"action": "invest", "min_amount": 2000, "period": "month"}', 'fas fa-crown', 'hard', 'active', 7),
('Eco Warrior Badge', 'Offset 100 tons of CO2 through your mining investments', 'one_time', 'bonus', 25.00, '{"action": "co2_offset", "min_tons": 100}', 'fas fa-leaf', 'hard', 'active', 8);

-- Insert system settings
INSERT INTO `system_settings` (`setting_key`, `setting_value`, `setting_type`, `description`, `category`, `is_public`) VALUES
('site_name', 'AstroGenix', 'string', 'Website name', 'general', 1),
('site_description', 'Eco-Friendly Cryptocurrency Mining Platform', 'string', 'Website description', 'general', 1),
('site_keywords', 'eco mining, cryptocurrency, renewable energy, sustainable investment', 'string', 'SEO keywords', 'general', 1),
('maintenance_mode', '0', 'boolean', 'Enable maintenance mode', 'general', 0),
('registration_enabled', '1', 'boolean', 'Allow new user registration', 'users', 1),
('email_verification_required', '1', 'boolean', 'Require email verification for new users', 'users', 0),
('min_investment_amount', '100.00', 'decimal', 'Minimum investment amount in USDT', 'investment', 1),
('max_investment_amount', '100000.00', 'decimal', 'Maximum investment amount in USDT', 'investment', 1),
('referral_commission_l1', '10.00', 'decimal', 'Level 1 referral commission percentage', 'referral', 0),
('referral_commission_l2', '5.00', 'decimal', 'Level 2 referral commission percentage', 'referral', 0),
('referral_commission_l3', '2.00', 'decimal', 'Level 3 referral commission percentage', 'referral', 0),
('daily_profit_time', '00:00:00', 'string', 'Time to calculate daily profits (UTC)', 'investment', 0),
('withdrawal_min_amount', '10.00', 'decimal', 'Minimum withdrawal amount', 'withdrawal', 1),
('withdrawal_fee_percentage', '2.00', 'decimal', 'Withdrawal fee percentage', 'withdrawal', 1),
('support_email', '<EMAIL>', 'string', 'Support email address', 'contact', 1),
('admin_email', '<EMAIL>', 'string', 'Admin email address', 'contact', 0),
('facebook_url', 'https://facebook.com/astrogenix', 'string', 'Facebook page URL', 'social', 1),
('twitter_url', 'https://twitter.com/astrogenix', 'string', 'Twitter profile URL', 'social', 1),
('instagram_url', 'https://instagram.com/astrogenix', 'string', 'Instagram profile URL', 'social', 1),
('linkedin_url', 'https://linkedin.com/company/astrogenix', 'string', 'LinkedIn company URL', 'social', 1);

-- Insert sample demo users for demonstration
INSERT INTO `users` (`username`, `email`, `password`, `first_name`, `last_name`, `balance`, `total_invested`, `total_earned`, `referral_code`, `referred_by`, `referral_earnings`, `role`, `status`, `email_verified`, `created_at`) VALUES
('john_eco', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'John', 'Smith', 1250.75, 5000.00, 750.25, 'ECO001', 1, 125.50, 'user', 'active', 1, DATE_SUB(NOW(), INTERVAL 30 DAY)),
('sarah_green', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Sarah', 'Johnson', 2100.30, 8500.00, 1200.80, 'ECO002', 1, 85.20, 'user', 'active', 1, DATE_SUB(NOW(), INTERVAL 25 DAY)),
('mike_solar', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Mike', 'Wilson', 850.60, 3200.00, 480.40, 'ECO003', 2, 45.30, 'user', 'active', 1, DATE_SUB(NOW(), INTERVAL 20 DAY)),
('emma_wind', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Emma', 'Davis', 1750.90, 6800.00, 920.15, 'ECO004', 2, 68.75, 'user', 'active', 1, DATE_SUB(NOW(), INTERVAL 15 DAY)),
('alex_hydro', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Alex', 'Brown', 3200.45, 12000.00, 1850.30, 'ECO005', 3, 156.80, 'user', 'active', 1, DATE_SUB(NOW(), INTERVAL 10 DAY));

COMMIT;
