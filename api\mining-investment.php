<?php
session_start();
require_once '../includes/functions.php';
require_once '../includes/auth.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

// Check if request method is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

$action = $_POST['action'] ?? '';

try {
    switch ($action) {
        case 'invest':
            $plan_id = intval($_POST['plan_id'] ?? 0);
            $amount = floatval($_POST['amount'] ?? 0);
            $user_id = $_SESSION['user_id'];
            
            // Validate input
            if ($plan_id <= 0 || $amount <= 0) {
                throw new Exception('Invalid plan ID or amount');
            }
            
            // Get mining plan
            $mining_plan = getMiningPlanById($plan_id);
            if (!$mining_plan) {
                throw new Exception('Mining plan not found');
            }
            
            // Check if plan is active
            if (!$mining_plan['is_active']) {
                throw new Exception('Mining plan is not active');
            }
            
            // Validate amount range
            if ($amount < $mining_plan['min_amount'] || $amount > $mining_plan['max_amount']) {
                throw new Exception("Investment amount must be between ${mining_plan['min_amount']} and ${mining_plan['max_amount']}");
            }
            
            // Get user data
            $user = getUserById($user_id);
            if (!$user) {
                throw new Exception('User not found');
            }
            
            // Check user balance
            if ($user['balance'] < $amount) {
                throw new Exception('Insufficient balance');
            }
            
            $db = getDB();
            $db->beginTransaction();
            
            try {
                // Deduct amount from user balance
                $success = updateUserBalance($user_id, $amount, 'subtract');
                if (!$success) {
                    throw new Exception('Failed to update user balance');
                }
                
                // Create mining investment
                $success = createUserMiningInvestment($user_id, $plan_id, $amount);
                if (!$success) {
                    throw new Exception('Failed to create mining investment');
                }
                
                // Update user mining power and eco score
                $mining_power_gained = $amount * ($mining_plan['mining_power_bonus'] / 100);
                $eco_score_gained = $mining_plan['eco_score_bonus'];
                
                $stmt = $db->prepare("
                    UPDATE users 
                    SET mining_power = mining_power + ?, 
                        eco_score = eco_score + ? 
                    WHERE id = ?
                ");
                $stmt->execute([$mining_power_gained, $eco_score_gained, $user_id]);
                
                // Process referral commissions if user was referred
                if ($user['referred_by']) {
                    processReferralCommissions($user['referred_by'], $amount, $user_id);
                }
                
                // Update eco-mining statistics
                $energy_generated = $amount * 0.1; // Example calculation
                $carbon_offset = $amount * 0.05;   // Example calculation
                $trees_equivalent = intval($amount / 10); // Example calculation
                
                updateEcoMiningStats($user_id, $energy_generated, $carbon_offset, $trees_equivalent);
                
                // Create transaction record
                $stmt = $db->prepare("
                    INSERT INTO transactions 
                    (user_id, type, amount, description, status) 
                    VALUES (?, 'mining_investment', ?, ?, 'completed')
                ");
                $description = "Investment in {$mining_plan['title']} mining plan";
                $stmt->execute([$user_id, $amount, $description]);
                
                $db->commit();
                
                echo json_encode([
                    'success' => true, 
                    'message' => 'Mining investment successful',
                    'data' => [
                        'amount' => $amount,
                        'plan_title' => $mining_plan['title'],
                        'daily_rate' => $mining_plan['daily_rate'],
                        'duration_days' => $mining_plan['duration_days'],
                        'mining_power_gained' => $mining_power_gained,
                        'eco_score_gained' => $eco_score_gained
                    ]
                ]);
                
            } catch (Exception $e) {
                $db->rollBack();
                throw $e;
            }
            break;
            
        case 'get_user_investments':
            $user_id = $_SESSION['user_id'];
            $investments = getUserMiningInvestments($user_id);
            
            echo json_encode([
                'success' => true,
                'data' => $investments
            ]);
            break;
            
        case 'calculate_profit':
            $plan_id = intval($_POST['plan_id'] ?? 0);
            $amount = floatval($_POST['amount'] ?? 0);
            
            if ($plan_id <= 0 || $amount <= 0) {
                throw new Exception('Invalid plan ID or amount');
            }
            
            $mining_plan = getMiningPlanById($plan_id);
            if (!$mining_plan) {
                throw new Exception('Mining plan not found');
            }
            
            $daily_profit = $amount * ($mining_plan['daily_rate'] / 100);
            $total_profit = $daily_profit * $mining_plan['duration_days'];
            $total_return = $amount + $total_profit;
            
            echo json_encode([
                'success' => true,
                'data' => [
                    'daily_profit' => round($daily_profit, 2),
                    'total_profit' => round($total_profit, 2),
                    'total_return' => round($total_return, 2),
                    'duration_days' => $mining_plan['duration_days']
                ]
            ]);
            break;
            
        default:
            throw new Exception('Invalid action');
    }
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}

// Helper function to process referral commissions
function processReferralCommissions($referrer_id, $amount, $referred_id) {
    $db = getDB();
    
    // Define commission rates for different levels
    $commission_rates = [
        1 => 0.10, // 10% for direct referrals
        2 => 0.05, // 5% for second level
        3 => 0.02  // 2% for third level
    ];
    
    $current_referrer_id = $referrer_id;
    $level = 1;
    
    while ($current_referrer_id && $level <= 3) {
        $referrer = getUserById($current_referrer_id);
        if (!$referrer) {
            break;
        }
        
        $commission_rate = $commission_rates[$level];
        $commission_amount = $amount * $commission_rate;
        
        // Add commission to referrer's balance
        updateUserBalance($current_referrer_id, $commission_amount, 'add');
        
        // Record referral earning
        addReferralEarning(
            $current_referrer_id, 
            $referred_id, 
            $level, 
            $commission_rate, 
            $commission_amount, 
            'mining_investment'
        );
        
        // Update referrer's total referral earnings
        $stmt = $db->prepare("
            UPDATE users 
            SET referral_earnings = referral_earnings + ? 
            WHERE id = ?
        ");
        $stmt->execute([$commission_amount, $current_referrer_id]);
        
        // Move to next level
        $current_referrer_id = $referrer['referred_by'];
        $level++;
    }
}
?>
