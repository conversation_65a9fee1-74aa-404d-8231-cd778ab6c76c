# 🌱 AstroGenix - Production Ready Platform

## Обзор проекта

**AstroGenix** - это современная платформа экологического майнинга криптовалют, полностью готовая к production развертыванию. Платформа построена на PHP/MySQL с полной мобильной оптимизацией, интерактивными анимациями и комплексной системой безопасности.

## 📦 Комплексный пакет установки

Создан полный пакет установки, включающий:
- **Автоматический мастер установки**: `/install/index.php` с пошаговой настройкой
- **Полная схема базы данных**: `/database/install.sql` с демо-данными
- **Подробная документация**: `/INSTALLATION-GUIDE.md` с инструкциями
- **Конфигурация-пример**: `/config/config.example.php` для быстрой настройки

## ✨ Features

### Core Platform
- **Eco-Mining Investment Plans**: Solar, Wind, Hydro, Geothermal, Biomass, and Hybrid mining options
- **Multi-Level Referral System**: 3-level commission structure (10%, 5%, 2%)
- **Task & Mission System**: Daily, weekly, and monthly tasks with USDT rewards
- **Real-time Dashboard**: Investment tracking, profit calculations, and portfolio management
- **Mobile Optimization**: Full responsive design for 320px-767px screens
- **Interactive Animations**: Eco-themed CSS/JS animations and particle effects

### Security & Performance
- **Production-Ready Security**: XSS protection, CSRF tokens, SQL injection prevention
- **Performance Optimized**: Minified CSS/JS, GZIP compression, caching
- **SSL/HTTPS Ready**: Force HTTPS, secure cookies, security headers
- **Rate Limiting**: API and form submission protection
- **Comprehensive Logging**: Error tracking and performance monitoring

### Administrative Tools
- **Mining Plan Management**: CRUD operations for investment plans
- **User Management**: User accounts, balances, and activity tracking
- **Task Management**: Create and manage platform tasks
- **Referral Analytics**: Commission tracking and leaderboards
- **System Monitoring**: Health checks and performance metrics

## 🚀 Quick Installation

### Prerequisites
- PHP 7.4+ (recommended: PHP 8.0+)
- MySQL 5.7+ or MariaDB 10.3+
- Apache/Nginx web server
- SSL certificate (for production)

### Installation Steps

1. **Upload Files**
   ```bash
   # Upload all files to your web server
   # Ensure proper ownership and permissions
   chown -R www-data:www-data /path/to/astrogenix
   ```

2. **Database Setup**
   ```bash
   # Import the database schema
   mysql -u username -p database_name < database/astrogenix_schema.sql
   ```

3. **Configuration**
   ```bash
   # Copy and edit production config
   cp config/production.php config/config.php
   
   # Update database credentials in config/config.php
   # Update SITE_URL, SITE_EMAIL, and other settings
   ```

4. **Set Permissions**
   ```bash
   # Set secure permissions
   find /path/to/astrogenix -type d -exec chmod 755 {} \;
   find /path/to/astrogenix -type f -exec chmod 644 {} \;
   
   # Make writable directories
   chmod 755 logs uploads cache backups
   chmod 600 config/config.php
   ```

5. **Web Server Configuration**

   **Apache (.htaccess included)**
   ```apache
   # Enable mod_rewrite and mod_headers
   # .htaccess file is already configured
   ```

   **Nginx**
   ```nginx
   server {
       listen 443 ssl http2;
       server_name your-domain.com;
       root /path/to/astrogenix;
       index index.php;
       
       # SSL configuration
       ssl_certificate /path/to/certificate.crt;
       ssl_certificate_key /path/to/private.key;
       
       # Security headers
       add_header X-Content-Type-Options nosniff;
       add_header X-Frame-Options DENY;
       add_header X-XSS-Protection "1; mode=block";
       
       location / {
           try_files $uri $uri/ /index.php?$query_string;
       }
       
       location ~ \.php$ {
           fastcgi_pass unix:/var/run/php/php8.0-fpm.sock;
           fastcgi_index index.php;
           fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
           include fastcgi_params;
       }
       
       # Deny access to sensitive files
       location ~ ^/(config|includes|logs|backups) {
           deny all;
       }
   }
   ```

## 🔧 Configuration

### Environment Settings
Update `config/config.php` with your production values:

```php
// Database
define('DB_HOST', 'your-db-host');
define('DB_NAME', 'your-db-name');
define('DB_USER', 'your-db-user');
define('DB_PASS', 'your-secure-password');

// Site
define('SITE_URL', 'https://your-domain.com');
define('SITE_EMAIL', '<EMAIL>');

// SMTP (for email notifications)
define('SMTP_HOST', 'your-smtp-host');
define('SMTP_USERNAME', 'your-smtp-user');
define('SMTP_PASSWORD', 'your-smtp-password');
```

### Admin Account
Create the first admin account:

```sql
INSERT INTO users (username, email, password, role, status) VALUES 
('admin', '<EMAIL>', '$2y$10$hashed_password', 'admin', 'active');
```

## 📱 Mobile Features

- **Touch-Optimized Interface**: 44px minimum touch targets
- **Swipe Navigation**: Left/right gestures for page navigation
- **Pull-to-Refresh**: Native-like refresh functionality
- **Virtual Keyboard Handling**: Automatic viewport adjustments
- **Floating Action Button**: Quick access to key features
- **Responsive Breakpoints**: 320px, 768px, 1024px, 1920px+

## 🎨 Customization

### Color Scheme
The platform uses CSS custom properties for easy theming:

```css
:root {
    --primary-green: #10b981;
    --primary-green-dark: #059669;
    --primary-purple: #8b5cf6;
    --primary-purple-dark: #7c3aed;
}
```

### Animation Settings
Animations can be controlled via CSS variables:

```css
:root {
    --animation-speed: 0.3s;
    --particle-count: 50;
    --energy-flow-speed: 2s;
}
```

## 🔒 Security Features

- **Input Validation**: All user inputs are validated and sanitized
- **SQL Injection Protection**: PDO prepared statements
- **XSS Prevention**: Output escaping with htmlspecialchars()
- **CSRF Protection**: Token-based form protection
- **Rate Limiting**: Prevents brute force attacks
- **Secure Sessions**: HTTPOnly and Secure cookie flags
- **File Upload Security**: Type and size validation

## 📊 Monitoring & Maintenance

### Health Check
Access `/health` endpoint for system status:

```json
{
    "status": "healthy",
    "timestamp": "2024-01-01T00:00:00Z",
    "checks": {
        "database": "healthy",
        "disk_space": "healthy"
    }
}
```

### Log Files
- **Application Logs**: `logs/app.log`
- **Error Logs**: `logs/error.log`
- **Access Logs**: Web server logs

### Backup Strategy
- **Database**: Daily automated backups
- **Files**: Weekly file system backups
- **Retention**: 30 days (configurable)

## 🚀 Performance Optimization

### Caching
- **File Caching**: Static content caching
- **Database Query Caching**: Frequently accessed data
- **Browser Caching**: CSS/JS/Image caching headers

### Optimization Features
- **Minified Assets**: CSS and JavaScript minification
- **GZIP Compression**: Server-side compression
- **Image Optimization**: WebP support where available
- **Lazy Loading**: Images and content lazy loading

## 🆘 Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check database credentials in config/config.php
   - Verify database server is running
   - Check firewall settings

2. **File Permission Errors**
   - Ensure web server has read access to files
   - Verify write permissions on logs, uploads, cache directories

3. **SSL Certificate Issues**
   - Verify certificate installation
   - Check certificate chain
   - Update FORCE_HTTPS setting if needed

### Support
For technical support and updates:
- Email: <EMAIL>
- Documentation: Available in `/docs` directory
- System logs: Check `/logs` directory

## 📄 License

This project is proprietary software. All rights reserved.

## 🔄 Updates

To update the platform:
1. Backup current installation
2. Upload new files
3. Run database migrations if needed
4. Clear cache and test functionality

---

**AstroGenix v1.0** - Sustainable Mining Investment Platform
Built with ❤️ for a greener future 🌱
