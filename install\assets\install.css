/* AstroGenix Installation Wizard Styles */

:root {
    --primary-color: #10b981;
    --primary-dark: #059669;
    --secondary-color: #8b5cf6;
    --secondary-dark: #7c3aed;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --light-bg: #f8fafc;
    --white: #ffffff;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-600: #475569;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    min-height: 100vh;
    color: var(--gray-800);
    line-height: 1.6;
}

.installer-container {
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
}

.installer-header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

.installer-header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.installer-header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

.progress-bar {
    background: rgba(255,255,255,0.2);
    border-radius: 15px;
    padding: 8px;
    margin: 25px 0;
    backdrop-filter: blur(10px);
}

.progress-steps {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}

.progress-step {
    flex: 1;
    text-align: center;
    padding: 12px 8px;
    color: rgba(255,255,255,0.7);
    font-size: 0.9rem;
    position: relative;
    border-radius: 10px;
    transition: all 0.3s ease;
    min-width: 120px;
}

.progress-step.active {
    color: white;
    font-weight: bold;
    background: rgba(255,255,255,0.2);
    transform: scale(1.05);
}

.progress-step.completed {
    color: var(--success-color);
    background: rgba(16, 185, 129, 0.2);
}

.installer-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    overflow: hidden;
    margin-bottom: 20px;
    backdrop-filter: blur(10px);
}

.step-content {
    padding: 50px;
}

.welcome-hero {
    text-align: center;
    margin-bottom: 50px;
}

.eco-icon {
    font-size: 5rem;
    margin-bottom: 25px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.welcome-hero h1 {
    font-size: 3rem;
    margin-bottom: 15px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.welcome-hero .lead {
    font-size: 1.3rem;
    color: var(--gray-600);
    margin-bottom: 0;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 25px;
    margin: 40px 0;
}

.feature-card {
    text-align: center;
    padding: 30px 20px;
    background: linear-gradient(135deg, var(--gray-100), var(--white));
    border-radius: 15px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 1px solid var(--gray-200);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.feature-card i {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 15px;
    display: block;
}

.feature-card h3 {
    margin-bottom: 10px;
    color: var(--gray-800);
    font-size: 1.1rem;
}

.feature-card p {
    color: var(--gray-600);
    font-size: 0.95rem;
}

.installation-info {
    background: linear-gradient(135deg, var(--light-bg), var(--gray-100));
    padding: 30px;
    border-radius: 15px;
    margin: 30px 0;
    border-left: 5px solid var(--primary-color);
}

.installation-info h3 {
    margin-bottom: 20px;
    color: var(--gray-800);
    font-size: 1.3rem;
}

.installation-info ul {
    list-style: none;
    padding-left: 0;
}

.installation-info li {
    padding: 8px 0;
    display: flex;
    align-items: center;
    color: var(--gray-700);
}

.installation-info li::before {
    content: '';
    width: 8px;
    height: 8px;
    background: var(--success-color);
    border-radius: 50%;
    margin-right: 12px;
    flex-shrink: 0;
}

.requirements-list {
    margin: 25px 0;
}

.requirement-item {
    display: flex;
    align-items: flex-start;
    padding: 20px;
    margin: 15px 0;
    border-radius: 12px;
    background: var(--gray-100);
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
}

.requirement-item.success {
    background: linear-gradient(135deg, #dcfce7, #bbf7d0);
    border-left-color: var(--success-color);
}

.requirement-item.warning {
    background: linear-gradient(135deg, #fef3c7, #fde68a);
    border-left-color: var(--warning-color);
}

.requirement-item.error {
    background: linear-gradient(135deg, #fee2e2, #fecaca);
    border-left-color: var(--danger-color);
}

.requirement-icon {
    margin-right: 20px;
    font-size: 1.4rem;
    flex-shrink: 0;
    margin-top: 2px;
}

.requirement-details h4 {
    margin-bottom: 8px;
    font-size: 1.1rem;
    color: var(--gray-800);
}

.requirement-details p {
    color: var(--gray-600);
    margin-bottom: 5px;
}

.requirement-details small {
    color: var(--gray-500);
    font-size: 0.85rem;
}

.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--gray-800);
    font-size: 1rem;
}

.form-group input {
    width: 100%;
    padding: 15px;
    border: 2px solid var(--gray-200);
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: var(--white);
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
    transform: translateY(-1px);
}

.form-group small {
    display: block;
    margin-top: 8px;
    color: var(--gray-600);
    font-size: 0.9rem;
    line-height: 1.4;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 15px 25px;
    border: none;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 8px;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
}

.btn-secondary {
    background: linear-gradient(135deg, var(--gray-600), var(--gray-800));
    color: white;
    box-shadow: 0 4px 15px rgba(71, 85, 105, 0.3);
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(71, 85, 105, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color), #059669);
    color: white;
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.btn-info {
    background: linear-gradient(135deg, var(--secondary-color), var(--secondary-dark));
    color: white;
    box-shadow: 0 4px 15px rgba(139, 92, 246, 0.3);
}

.btn-lg {
    padding: 18px 35px;
    font-size: 1.2rem;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.step-actions {
    text-align: center;
    margin-top: 40px;
    padding-top: 30px;
    border-top: 2px solid var(--gray-200);
}

.alert {
    padding: 20px;
    border-radius: 12px;
    margin: 25px 0;
    border-left: 5px solid;
}

.alert h4 {
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.alert ul {
    margin: 10px 0;
    padding-left: 20px;
}

.alert li {
    margin: 5px 0;
}

.alert-success {
    background: linear-gradient(135deg, #dcfce7, #bbf7d0);
    color: #166534;
    border-left-color: var(--success-color);
}

.alert-warning {
    background: linear-gradient(135deg, #fef3c7, #fde68a);
    color: #92400e;
    border-left-color: var(--warning-color);
}

.alert-danger {
    background: linear-gradient(135deg, #fee2e2, #fecaca);
    color: #991b1b;
    border-left-color: var(--danger-color);
}

.installation-progress {
    margin: 30px 0;
}

.install-step {
    display: flex;
    align-items: flex-start;
    padding: 20px;
    margin: 15px 0;
    border-radius: 12px;
    background: var(--gray-100);
    transition: all 0.3s ease;
    border-left: 4px solid var(--gray-300);
}

.install-step.success {
    background: linear-gradient(135deg, #dcfce7, #bbf7d0);
    border-left-color: var(--success-color);
}

.install-step.error {
    background: linear-gradient(135deg, #fee2e2, #fecaca);
    border-left-color: var(--danger-color);
}

.step-icon {
    margin-right: 20px;
    font-size: 1.4rem;
    flex-shrink: 0;
    margin-top: 2px;
}

.step-details h4 {
    margin-bottom: 8px;
    color: var(--gray-800);
    font-size: 1.1rem;
}

.step-details p {
    color: var(--gray-600);
    line-height: 1.5;
}

.completion-hero {
    text-align: center;
    margin-bottom: 50px;
}

.success-icon {
    font-size: 5rem;
    margin-bottom: 25px;
    animation: bounce 1s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

.completion-hero h1 {
    font-size: 2.5rem;
    margin-bottom: 15px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.completion-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
    margin: 40px 0;
}

.info-card {
    background: linear-gradient(135deg, var(--light-bg), var(--gray-100));
    padding: 30px;
    border-radius: 15px;
    border-left: 5px solid var(--primary-color);
    transition: transform 0.3s ease;
}

.info-card:hover {
    transform: translateY(-3px);
}

.info-card h3 {
    margin-bottom: 20px;
    color: var(--primary-color);
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.info-card ul {
    list-style: none;
    padding-left: 0;
}

.info-card li {
    padding: 8px 0;
    color: var(--gray-600);
    display: flex;
    align-items: flex-start;
    gap: 10px;
}

.info-card li::before {
    content: '•';
    color: var(--primary-color);
    font-weight: bold;
    flex-shrink: 0;
}

.quick-links {
    text-align: center;
    margin: 40px 0;
}

.support-info {
    text-align: center;
    margin-top: 40px;
    padding: 30px;
    background: linear-gradient(135deg, var(--light-bg), var(--gray-100));
    border-radius: 15px;
    border: 2px solid var(--gray-200);
}

.support-info h3 {
    margin-bottom: 15px;
    color: var(--gray-800);
}

.support-info p {
    color: var(--gray-600);
    line-height: 1.8;
}

.support-info code {
    background: var(--gray-200);
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    color: var(--gray-800);
}

.text-success { color: var(--success-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-danger { color: var(--danger-color) !important; }

/* Responsive Design */
@media (max-width: 768px) {
    .installer-container {
        padding: 15px;
    }
    
    .step-content {
        padding: 30px 25px;
    }
    
    .welcome-hero h1 {
        font-size: 2.2rem;
    }
    
    .eco-icon, .success-icon {
        font-size: 3.5rem;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .completion-info {
        grid-template-columns: 1fr;
    }
    
    .quick-links .btn {
        display: block;
        margin: 15px auto;
        max-width: 250px;
    }
    
    .progress-steps {
        flex-direction: column;
        gap: 10px;
    }
    
    .progress-step {
        min-width: auto;
        width: 100%;
    }
    
    .installer-header h1 {
        font-size: 2rem;
        flex-direction: column;
        gap: 10px;
    }
}

@media (max-width: 480px) {
    .installer-container {
        padding: 10px;
    }
    
    .step-content {
        padding: 20px 15px;
    }
    
    .btn {
        padding: 12px 20px;
        font-size: 0.95rem;
    }
    
    .btn-lg {
        padding: 15px 25px;
        font-size: 1.1rem;
    }
    
    .form-group input {
        padding: 12px;
    }
    
    .requirement-item,
    .install-step {
        padding: 15px;
    }
    
    .feature-card {
        padding: 20px 15px;
    }
    
    .info-card {
        padding: 20px;
    }
}
