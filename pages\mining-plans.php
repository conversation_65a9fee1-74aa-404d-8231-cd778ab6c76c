<?php
session_start();
require_once '../includes/functions.php';
require_once '../includes/auth.php';

// Check if user is logged in
if (!isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$page_title = 'Eco-Mining Plans';
$page_description = 'Choose from our sustainable mining plans powered by renewable energy sources.';

// Get all active mining plans
try {
    $mining_plans = getAllMiningPlans(true);
} catch (Exception $e) {
    echo "Error loading mining plans: " . $e->getMessage();
    $mining_plans = [];
}

// Get user data
$user = getUserById($_SESSION['user_id']);

include '../includes/header.php';
?>

<div class="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 hero-pattern">
    <?php include '../includes/navbar.php'; ?>
    
    <!-- Hero Section -->
    <section class="pt-32 pb-20">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16">
                <span class="inline-block px-6 py-3 bg-white bg-opacity-15 rounded-full text-sm font-medium mb-4 backdrop-blur-sm border border-white border-opacity-20 shadow-lg eco-glow">
                    🌱 Sustainable Mining
                </span>
                <h1 class="text-5xl lg:text-6xl font-bold mb-8 text-white leading-tight">
                    Choose Your
                    <span class="text-transparent bg-clip-text bg-gradient-to-r from-green-400 to-purple-400">Eco-Mining</span>
                    Plan
                </h1>
                <p class="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
                    Start mining cryptocurrency with 100% renewable energy. Choose from solar, wind, hydro, and other sustainable energy sources.
                </p>
                
                <!-- User Balance -->
                <div class="bg-white bg-opacity-10 backdrop-blur-sm rounded-2xl p-6 max-w-md mx-auto border border-white border-opacity-20">
                    <div class="text-gray-300 text-sm mb-2">Available Balance</div>
                    <div class="text-3xl font-bold text-white">$<?php echo number_format($user['balance'], 2); ?></div>
                    <div class="text-green-400 text-sm mt-2">
                        <i class="fas fa-leaf mr-1"></i>
                        Eco Score: <?php echo number_format($user['eco_score']); ?>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Mining Plans Grid -->
    <section class="pb-20">
        <div class="container mx-auto px-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <?php foreach ($mining_plans as $plan): ?>
                <div class="mining-card bg-white bg-opacity-10 backdrop-blur-sm rounded-2xl p-8 border border-white border-opacity-20 hover:bg-opacity-15 transition-all duration-300 group">
                    <!-- Plan Icon -->
                    <div class="text-center mb-6">
                        <div class="w-20 h-20 mx-auto bg-gradient-to-br from-green-400 to-purple-500 rounded-full flex items-center justify-center mb-4 group-hover:scale-110 transition-transform">
                            <i class="<?php echo htmlspecialchars($plan['icon_class']); ?> text-3xl text-white"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-white mb-2"><?php echo htmlspecialchars($plan['title']); ?></h3>
                        <span class="inline-block px-3 py-1 bg-green-500 bg-opacity-20 text-green-400 rounded-full text-sm font-medium">
                            <?php echo ucfirst(str_replace('_', ' ', $plan['category'])); ?>
                        </span>
                    </div>

                    <!-- Plan Details -->
                    <div class="space-y-4 mb-8">
                        <div class="flex justify-between items-center">
                            <span class="text-gray-300">Daily Rate</span>
                            <span class="text-green-400 font-bold"><?php echo number_format($plan['daily_rate'], 2); ?>%</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-300">Duration</span>
                            <span class="text-white font-semibold"><?php echo $plan['duration_days']; ?> days</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-300">Min Investment</span>
                            <span class="text-white font-semibold">$<?php echo number_format($plan['min_amount']); ?></span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-300">Max Investment</span>
                            <span class="text-white font-semibold">$<?php echo number_format($plan['max_amount']); ?></span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-300">Mining Power Bonus</span>
                            <span class="text-purple-400 font-semibold">+<?php echo $plan['mining_power_bonus']; ?>%</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-300">Eco Score Bonus</span>
                            <span class="text-green-400 font-semibold">+<?php echo $plan['eco_score_bonus']; ?></span>
                        </div>
                    </div>

                    <!-- Plan Description -->
                    <div class="mb-8">
                        <p class="text-gray-300 text-sm leading-relaxed">
                            <?php echo htmlspecialchars($plan['description']); ?>
                        </p>
                    </div>

                    <!-- Investment Form -->
                    <form class="investment-form" data-plan-id="<?php echo $plan['id']; ?>" data-min="<?php echo $plan['min_amount']; ?>" data-max="<?php echo $plan['max_amount']; ?>">
                        <div class="mb-4">
                            <label class="block text-gray-300 text-sm font-medium mb-2">Investment Amount ($)</label>
                            <input type="number" 
                                   name="amount" 
                                   min="<?php echo $plan['min_amount']; ?>" 
                                   max="<?php echo $plan['max_amount']; ?>" 
                                   step="0.01"
                                   class="w-full px-4 py-3 bg-white bg-opacity-10 border border-white border-opacity-20 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                                   placeholder="Enter amount"
                                   required>
                        </div>
                        
                        <!-- Profit Calculator -->
                        <div class="bg-green-500 bg-opacity-10 rounded-xl p-4 mb-6 border border-green-500 border-opacity-20">
                            <div class="text-green-400 text-sm font-medium mb-2">Estimated Returns</div>
                            <div class="profit-calculation text-white">
                                <div class="flex justify-between text-sm">
                                    <span>Daily Profit:</span>
                                    <span class="daily-profit">$0.00</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span>Total Profit:</span>
                                    <span class="total-profit font-bold">$0.00</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span>Total Return:</span>
                                    <span class="total-return font-bold text-green-400">$0.00</span>
                                </div>
                            </div>
                        </div>

                        <button type="submit" class="btn-eco w-full text-white py-4 rounded-xl font-bold transition-all duration-300 transform hover:scale-105">
                            <i class="fas fa-leaf mr-2"></i>
                            Start Mining
                        </button>
                    </form>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize eco animations
    if (typeof initEcoAnimations === 'function') {
        initEcoAnimations();
    }
    
    // Profit calculator
    document.querySelectorAll('.investment-form input[name="amount"]').forEach(input => {
        input.addEventListener('input', function() {
            const form = this.closest('.investment-form');
            const amount = parseFloat(this.value) || 0;
            const planCard = this.closest('.mining-card');
            const dailyRate = parseFloat(planCard.querySelector('.text-green-400').textContent) / 100;
            const duration = parseInt(planCard.querySelector('.text-white.font-semibold').textContent);
            
            const dailyProfit = amount * dailyRate;
            const totalProfit = dailyProfit * duration;
            const totalReturn = amount + totalProfit;
            
            form.querySelector('.daily-profit').textContent = '$' + dailyProfit.toFixed(2);
            form.querySelector('.total-profit').textContent = '$' + totalProfit.toFixed(2);
            form.querySelector('.total-return').textContent = '$' + totalReturn.toFixed(2);
        });
    });
    
    // Investment form submission
    document.querySelectorAll('.investment-form').forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const planId = this.dataset.planId;
            const amount = parseFloat(this.querySelector('input[name="amount"]').value);
            const minAmount = parseFloat(this.dataset.min);
            const maxAmount = parseFloat(this.dataset.max);
            
            // Validation
            if (amount < minAmount || amount > maxAmount) {
                alert(`Investment amount must be between $${minAmount} and $${maxAmount}`);
                return;
            }
            
            // Check user balance
            const userBalance = <?php echo $user['balance']; ?>;
            if (amount > userBalance) {
                alert('Insufficient balance. Please deposit funds first.');
                return;
            }
            
            // Submit investment
            const formData = new FormData();
            formData.append('action', 'invest');
            formData.append('plan_id', planId);
            formData.append('amount', amount);
            
            fetch('../api/mining-investment.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Investment successful! Your eco-mining has started.');
                    location.reload();
                } else {
                    alert('Investment failed: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred. Please try again.');
            });
        });
    });
});
</script>

<?php include '../includes/footer.php'; ?>
