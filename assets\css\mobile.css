/* Mobile-specific styles for AstroGenix Eco-Mining Platform */

/* Mobile Navigation Enhancements */
@media (max-width: 768px) {
    .navbar-glass {
        backdrop-filter: blur(15px);
        background: rgba(255, 255, 255, 0.95);
    }
    
    /* Hero Section Mobile */
    .hero-section {
        min-height: 100vh;
        padding: 2rem 0;
    }
    
    .hero-title {
        font-size: 2.5rem;
        line-height: 1.2;
        margin-bottom: 1.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.125rem;
        margin-bottom: 2rem;
    }
    
    /* Stats Grid Mobile */
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }
    
    /* Features Mobile */
    .features-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .feature-card {
        padding: 1.5rem;
    }
    
    /* Mining Plans Cards Mobile */
    .mining-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .mining-card {
        margin-bottom: 1rem;
    }
    
    /* Blog Cards Mobile */
    .blog-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    /* Footer Mobile */
    .footer-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }
    
    .footer-newsletter {
        flex-direction: column;
        align-items: stretch;
    }
    
    .footer-newsletter input {
        margin-bottom: 1rem;
        border-radius: 0.75rem;
    }
    
    .footer-newsletter button {
        border-radius: 0.75rem;
    }
    
    /* Button Improvements */
    .btn-primary,
    .btn-secondary {
        padding: 1rem 1.5rem;
        font-size: 1rem;
        min-height: 48px; /* Touch target size */
    }
    
    /* Form Improvements */
    input[type="email"],
    input[type="text"],
    input[type="password"],
    textarea {
        min-height: 48px;
        font-size: 16px; /* Prevent zoom on iOS */
    }
    
    /* Card Hover Effects - Reduced for mobile */
    .card-hover:hover {
        transform: translateY(-4px);
    }
    
    /* Floating Elements - Smaller on mobile */
    .animate-float {
        animation-duration: 8s;
    }
    
    /* Text Sizes */
    .text-5xl {
        font-size: 2.5rem;
    }
    
    .text-6xl {
        font-size: 3rem;
    }
    
    .text-7xl {
        font-size: 3.5rem;
    }
    
    /* Spacing Adjustments */
    .py-24 {
        padding-top: 3rem;
        padding-bottom: 3rem;
    }
    
    .py-20 {
        padding-top: 2.5rem;
        padding-bottom: 2.5rem;
    }
    
    /* Mobile Menu Enhancements */
    #mobile-menu {
        backdrop-filter: blur(20px);
        background: rgba(255, 255, 255, 0.98);
    }
    
    /* Flash Messages Mobile */
    .flash-message {
        position: fixed;
        top: 5rem;
        left: 1rem;
        right: 1rem;
        max-width: none;
        z-index: 60;
    }
}

/* Tablet Styles */
@media (min-width: 769px) and (max-width: 1024px) {
    .features-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .investment-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .blog-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
    .card-hover:hover {
        transform: none;
    }
    
    .btn-primary:hover,
    .btn-secondary:hover {
        transform: none;
    }
    
    /* Increase touch targets */
    button,
    .btn-primary,
    .btn-secondary,
    a[role="button"] {
        min-height: 44px;
        min-width: 44px;
    }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .hero-pattern {
        background-size: 50px 50px;
    }
}

/* Landscape Mobile */
@media (max-width: 768px) and (orientation: landscape) {
    .hero-section {
        min-height: 80vh;
    }
    
    .hero-title {
        font-size: 2rem;
    }
    
    .py-24 {
        padding-top: 2rem;
        padding-bottom: 2rem;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .navbar-glass {
        background: rgba(15, 23, 42, 0.95);
        border-bottom: 1px solid rgba(59, 130, 246, 0.2);
    }
    
    #mobile-menu {
        background: rgba(15, 23, 42, 0.98);
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .animate-float,
    .animate-pulse-slow {
        animation: none;
    }
    
    .card-hover {
        transition: none;
    }
    
    * {
        transition-duration: 0.01ms !important;
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
    }
}

/* Mobile Viewport and Keyboard Handling */
:root {
    --vh: 1vh;
}

.mobile-viewport {
    height: calc(var(--vh, 1vh) * 100);
}

.keyboard-open {
    height: auto;
    min-height: auto;
}

.keyboard-open .fab {
    bottom: 1rem;
}

/* Enhanced Mobile Styles for AstroGenix Pages */
@media (max-width: 767px) {
    /* Referrals Page Mobile */
    .referral-link-card {
        flex-direction: column;
        text-align: center;
    }

    .referral-link-card .flex {
        flex-direction: column;
        space-y: 1rem;
    }

    .referral-stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .leaderboard-table {
        font-size: 0.875rem;
    }

    .leaderboard-table th,
    .leaderboard-table td {
        padding: 0.5rem 0.25rem;
    }

    /* Tasks Page Mobile */
    .task-categories {
        grid-template-columns: 1fr;
    }

    .task-card {
        padding: 1rem;
    }

    .task-reward-display {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .task-action-button {
        width: 100%;
        justify-content: center;
    }

    /* Mining Plans Mobile Enhancements */
    .mining-plan-calculator {
        padding: 1rem;
    }

    .calculator-inputs {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .mining-plan-features {
        grid-template-columns: 1fr;
    }

    /* Dashboard Mobile */
    .dashboard-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .dashboard-chart {
        height: 250px;
    }

    /* Form Enhancements */
    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-input {
        padding: 0.875rem;
        font-size: 1rem;
    }

    .btn-primary {
        padding: 0.875rem 1.5rem;
        font-size: 1rem;
        width: 100%;
    }

    /* Modal Mobile */
    .modal-content {
        margin: 1rem;
        max-height: calc(100vh - 2rem);
        overflow-y: auto;
    }

    /* Touch Enhancements */
    .touch-target {
        min-height: 44px;
        min-width: 44px;
    }

    .card-hover:hover {
        transform: none;
    }

    .card-hover:active {
        transform: scale(0.98);
        transition: transform 0.1s ease;
    }

    /* Floating Action Button */
    .fab {
        position: fixed;
        bottom: 2rem;
        right: 2rem;
        width: 56px;
        height: 56px;
        border-radius: 50%;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        border: none;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        z-index: 1000;
        transition: all 0.3s ease;
    }

    .fab:hover {
        transform: scale(1.1);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
    }

    /* Swipe Gestures */
    .swipe-container {
        overflow-x: auto;
        scroll-snap-type: x mandatory;
        -webkit-overflow-scrolling: touch;
    }

    .swipe-item {
        scroll-snap-align: start;
        flex-shrink: 0;
    }

    /* Mobile Navigation Improvements */
    .mobile-nav-item {
        padding: 1rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .mobile-nav-item:last-child {
        border-bottom: none;
    }

    /* Eco-Mining Animations Mobile */
    .eco-particle {
        animation-duration: 4s;
    }

    .energy-pulse {
        animation-duration: 3s;
    }

    /* Reduced Motion */
    @media (prefers-reduced-motion: reduce) {
        .eco-particle,
        .energy-pulse,
        .animate-float {
            animation: none;
        }

        .card-hover:active {
            transform: none;
        }
    }
}

/* Ultra Small Screens (320px-480px) */
@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .referral-stats-grid {
        grid-template-columns: 1fr;
    }

    .dashboard-stats {
        grid-template-columns: 1fr;
    }

    .modal-content {
        margin: 0.5rem;
    }

    .fab {
        bottom: 1rem;
        right: 1rem;
        width: 48px;
        height: 48px;
        font-size: 1.25rem;
    }
}

/* Print Styles */
@media print {
    .navbar-glass,
    #mobile-menu,
    .flash-message,
    .animate-float,
    .fab {
        display: none !important;
    }

    .gradient-primary,
    .gradient-secondary {
        background: var(--primary-color) !important;
        color: white !important;
    }
}
