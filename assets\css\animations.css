/* AstroGenix Eco-Mining Animations */

/* Keyframe Animations */
@keyframes eco-pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0.8;
    }
    50% {
        transform: scale(1.05);
        opacity: 1;
    }
}

@keyframes energy-flow {
    0% {
        transform: translateX(-100%) rotate(0deg);
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        transform: translateX(100vw) rotate(360deg);
        opacity: 0;
    }
}

@keyframes mining-progress {
    0% {
        width: 0%;
        background-position: 0% 50%;
    }
    100% {
        width: 100%;
        background-position: 100% 50%;
    }
}

@keyframes leaf-growth {
    0% {
        transform: scale(0) rotate(-45deg);
        opacity: 0;
    }
    50% {
        transform: scale(0.8) rotate(0deg);
        opacity: 0.8;
    }
    100% {
        transform: scale(1) rotate(0deg);
        opacity: 1;
    }
}

@keyframes carbon-absorption {
    0% {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
    100% {
        transform: translateY(-50px) scale(0.3);
        opacity: 0;
    }
}

@keyframes solar-rays {
    0% {
        transform: rotate(0deg) scale(1);
        opacity: 0.3;
    }
    50% {
        opacity: 0.8;
    }
    100% {
        transform: rotate(360deg) scale(1.1);
        opacity: 0.3;
    }
}

@keyframes wind-turbine {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes water-flow {
    0% {
        transform: translateX(-20px);
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        transform: translateX(20px);
        opacity: 0;
    }
}

@keyframes eco-glow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(16, 185, 129, 0.3);
    }
    50% {
        box-shadow: 0 0 20px rgba(16, 185, 129, 0.6), 0 0 30px rgba(16, 185, 129, 0.4);
    }
}

@keyframes profit-counter {
    0% {
        transform: translateY(10px);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes floating-particles {
    0% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.3;
    }
    33% {
        transform: translateY(-10px) rotate(120deg);
        opacity: 0.8;
    }
    66% {
        transform: translateY(-5px) rotate(240deg);
        opacity: 0.6;
    }
    100% {
        transform: translateY(0px) rotate(360deg);
        opacity: 0.3;
    }
}

/* Animation Classes */
.eco-pulse {
    animation: eco-pulse 2s ease-in-out infinite;
}

.energy-flow {
    animation: energy-flow 3s linear infinite;
}

.mining-progress {
    animation: mining-progress 2s ease-out forwards;
}

.leaf-growth {
    animation: leaf-growth 1s ease-out forwards;
}

.carbon-absorption {
    animation: carbon-absorption 2s ease-in forwards;
}

.solar-rays {
    animation: solar-rays 8s linear infinite;
}

.wind-turbine {
    animation: wind-turbine 3s linear infinite;
}

.water-flow {
    animation: water-flow 2s ease-in-out infinite;
}

.eco-glow {
    animation: eco-glow 3s ease-in-out infinite;
}

.profit-counter {
    animation: profit-counter 0.5s ease-out forwards;
}

.floating-particles {
    animation: floating-particles 4s ease-in-out infinite;
}

/* Interactive Hover Effects */
.mining-card:hover {
    transform: translateY(-5px);
    transition: all 0.3s ease;
}

.mining-card:hover .eco-icon {
    animation: eco-pulse 1s ease-in-out infinite;
}

.energy-button:hover {
    background: linear-gradient(45deg, #10b981, #8b5cf6);
    box-shadow: 0 0 20px rgba(16, 185, 129, 0.4);
    transition: all 0.3s ease;
}

.eco-stat:hover .stat-icon {
    animation: leaf-growth 0.5s ease-out;
}

/* Progress Bars with Eco Theme */
.eco-progress-bar {
    background: linear-gradient(90deg, #10b981, #34d399, #10b981);
    background-size: 200% 100%;
    animation: mining-progress 3s ease-out infinite;
    border-radius: 10px;
    height: 8px;
    overflow: hidden;
    position: relative;
}

.eco-progress-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: energy-flow 2s linear infinite;
}

/* Particle System */
.particle-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    overflow: hidden;
}

.eco-particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: radial-gradient(circle, #10b981, #34d399);
    border-radius: 50%;
    animation: floating-particles 6s ease-in-out infinite;
}

.eco-particle:nth-child(2n) {
    background: radial-gradient(circle, #8b5cf6, #a78bfa);
    animation-delay: -2s;
    animation-duration: 8s;
}

.eco-particle:nth-child(3n) {
    background: radial-gradient(circle, #059669, #10b981);
    animation-delay: -4s;
    animation-duration: 7s;
}

/* Energy Orbs */
.energy-orb {
    position: relative;
    display: inline-block;
}

.energy-orb::before {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    background: radial-gradient(circle, rgba(16, 185, 129, 0.3), transparent);
    border-radius: 50%;
    animation: eco-pulse 2s ease-in-out infinite;
}

/* Mining Equipment Animations */
.solar-panel {
    position: relative;
    background: linear-gradient(45deg, #fbbf24, #f59e0b);
    border-radius: 8px;
    overflow: hidden;
}

.solar-panel::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(from 0deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: solar-rays 6s linear infinite;
}

.wind-turbine-blade {
    transform-origin: center;
    animation: wind-turbine 2s linear infinite;
}

.hydro-flow {
    background: linear-gradient(90deg, #06b6d4, #0891b2, #06b6d4);
    background-size: 200% 100%;
    animation: water-flow 3s ease-in-out infinite;
}

/* Carbon Offset Visualization */
.carbon-molecule {
    position: relative;
    display: inline-block;
}

.carbon-molecule.absorbed {
    animation: carbon-absorption 1.5s ease-in forwards;
}

/* Eco Score Counter */
.eco-counter {
    position: relative;
    overflow: hidden;
}

.eco-counter .digit {
    display: inline-block;
    animation: profit-counter 0.3s ease-out forwards;
}

.eco-counter .digit:nth-child(2) { animation-delay: 0.1s; }
.eco-counter .digit:nth-child(3) { animation-delay: 0.2s; }
.eco-counter .digit:nth-child(4) { animation-delay: 0.3s; }

/* Responsive Animation Adjustments */
@media (max-width: 768px) {
    .eco-pulse {
        animation-duration: 3s;
    }
    
    .energy-flow {
        animation-duration: 4s;
    }
    
    .floating-particles {
        animation-duration: 5s;
    }
    
    .mining-card:hover {
        transform: none;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    .eco-pulse,
    .energy-flow,
    .mining-progress,
    .leaf-growth,
    .carbon-absorption,
    .solar-rays,
    .wind-turbine,
    .water-flow,
    .eco-glow,
    .floating-particles {
        animation: none;
    }
    
    .mining-card:hover {
        transform: none;
    }
    
    .energy-button:hover {
        transition: none;
    }
}
