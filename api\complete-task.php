<?php
require_once '../config/config.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Необходима авторизация']);
    exit;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Метод не разрешен']);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['task_id']) || !is_numeric($input['task_id'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Неверный ID задания']);
    exit;
}

$task_id = (int)$input['task_id'];
$user_id = getCurrentUserId();

try {
    $db = getDB();
    $db->beginTransaction();

    // Get task details
    $stmt = $db->prepare("
        SELECT * FROM tasks 
        WHERE id = ? AND is_active = 1 
        AND (start_date IS NULL OR start_date <= CURDATE())
        AND (end_date IS NULL OR end_date >= CURDATE())
    ");
    $stmt->execute([$task_id]);
    $task = $stmt->fetch();

    if (!$task) {
        throw new Exception('Задание не найдено или неактивно');
    }

    // Check if user has already completed this task
    $stmt = $db->prepare("
        SELECT * FROM user_task_completions 
        WHERE user_id = ? AND task_id = ?
    ");
    $stmt->execute([$user_id, $task_id]);
    $completion = $stmt->fetch();

    // Check completion limits
    if ($task['task_type'] === 'one_time' && $completion) {
        throw new Exception('Это задание можно выполнить только один раз');
    }

    if ($task['task_type'] === 'daily' && $completion) {
        $last_completion = new DateTime($completion['completed_at']);
        $today = new DateTime();
        if ($last_completion->format('Y-m-d') === $today->format('Y-m-d')) {
            throw new Exception('Ежедневное задание уже выполнено сегодня');
        }
    }

    if ($task['task_type'] === 'weekly' && $completion) {
        $last_completion = new DateTime($completion['completed_at']);
        $this_week_start = new DateTime();
        $this_week_start->modify('monday this week');
        if ($last_completion >= $this_week_start) {
            throw new Exception('Еженедельное задание уже выполнено на этой неделе');
        }
    }

    if ($task['task_type'] === 'monthly' && $completion) {
        $last_completion = new DateTime($completion['completed_at']);
        $this_month_start = new DateTime();
        $this_month_start->modify('first day of this month');
        if ($last_completion >= $this_month_start) {
            throw new Exception('Месячное задание уже выполнено в этом месяце');
        }
    }

    if ($completion && $completion['times_completed'] >= $task['max_completions']) {
        throw new Exception('Достигнут лимит выполнений для этого задания');
    }

    // Validate task requirements based on type
    $user = getUserById($user_id);
    
    if ($task['task_type'] === 'referral') {
        // Check if user has required number of referrals
        if ($task['requirements']) {
            $requirements = json_decode($task['requirements'], true);
            if (isset($requirements['min_referrals']) && $user['total_referrals'] < $requirements['min_referrals']) {
                throw new Exception('Недостаточно рефералов для выполнения задания');
            }
        }
    }

    if ($task['task_type'] === 'investment') {
        // Check if user has required investment amount
        if ($task['requirements']) {
            $requirements = json_decode($task['requirements'], true);
            if (isset($requirements['min_investment'])) {
                $stmt = $db->prepare("
                    SELECT COALESCE(SUM(amount), 0) as total_invested 
                    FROM user_mining_investments 
                    WHERE user_id = ? AND is_active = 1
                ");
                $stmt->execute([$user_id]);
                $investment_data = $stmt->fetch();
                
                if ($investment_data['total_invested'] < $requirements['min_investment']) {
                    throw new Exception('Недостаточная сумма инвестиций для выполнения задания');
                }
            }
        }
    }

    // Record task completion
    if ($completion) {
        // Update existing completion
        $stmt = $db->prepare("
            UPDATE user_task_completions 
            SET completed_at = NOW(), times_completed = times_completed + 1
            WHERE user_id = ? AND task_id = ?
        ");
        $stmt->execute([$user_id, $task_id]);
    } else {
        // Create new completion record
        $stmt = $db->prepare("
            INSERT INTO user_task_completions (user_id, task_id, completed_at, times_completed)
            VALUES (?, ?, NOW(), 1)
        ");
        $stmt->execute([$user_id, $task_id]);
    }

    // Apply rewards
    $reward_message = '';
    
    if ($task['reward_type'] === 'usdt') {
        // Add USDT to user balance
        $stmt = $db->prepare("UPDATE users SET balance = balance + ? WHERE id = ?");
        $stmt->execute([$task['reward_amount'], $user_id]);
        $reward_message = "Получено " . number_format($task['reward_amount'], 2) . " USDT";
        
    } elseif ($task['reward_type'] === 'mining_power') {
        // Add mining power
        $stmt = $db->prepare("UPDATE users SET mining_power = mining_power + ? WHERE id = ?");
        $stmt->execute([$task['reward_amount'], $user_id]);
        $reward_message = "Получено " . number_format($task['reward_amount'], 2) . " майнинг силы";
        
    } elseif ($task['reward_type'] === 'eco_score') {
        // Add eco score
        $stmt = $db->prepare("UPDATE users SET eco_score = eco_score + ? WHERE id = ?");
        $stmt->execute([$task['reward_amount'], $user_id]);
        $reward_message = "Получено " . number_format($task['reward_amount']) . " эко-счета";
        
    } elseif ($task['reward_type'] === 'level_xp') {
        // Add experience points (if you have a level system)
        $stmt = $db->prepare("UPDATE users SET level_xp = level_xp + ? WHERE id = ?");
        $stmt->execute([$task['reward_amount'], $user_id]);
        $reward_message = "Получено " . number_format($task['reward_amount']) . " опыта";
    }

    // Log the task completion
    $stmt = $db->prepare("
        INSERT INTO activity_logs (user_id, action, description, created_at)
        VALUES (?, 'task_completed', ?, NOW())
    ");
    $stmt->execute([
        $user_id, 
        "Выполнено задание: " . $task['title'] . " - " . $reward_message
    ]);

    $db->commit();

    echo json_encode([
        'success' => true,
        'message' => 'Задание выполнено! ' . $reward_message,
        'task_title' => $task['title'],
        'reward_type' => $task['reward_type'],
        'reward_amount' => $task['reward_amount']
    ]);

} catch (Exception $e) {
    $db->rollBack();
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} catch (PDOException $e) {
    $db->rollBack();
    error_log("Database error in complete-task.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Ошибка базы данных'
    ]);
}
?>
