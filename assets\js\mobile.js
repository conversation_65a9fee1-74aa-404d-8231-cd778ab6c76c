/**
 * Mobile-specific JavaScript for AstroGenix Eco-Mining Platform
 * Enhanced touch interactions and mobile optimizations
 */

// Touch and gesture handling
class MobileEnhancements {
    constructor() {
        this.init();
    }

    init() {
        this.setupTouchEnhancements();
        this.setupSwipeGestures();
        this.setupMobileOptimizations();
        this.setupPullToRefresh();
        this.setupVirtualKeyboardHandling();
    }

    setupTouchEnhancements() {
        // Add touch feedback to buttons
        document.addEventListener('touchstart', (e) => {
            if (e.target.matches('button, .btn, .card-hover, .touch-target')) {
                e.target.style.transform = 'scale(0.98)';
                e.target.style.transition = 'transform 0.1s ease';
            }
        });

        document.addEventListener('touchend', (e) => {
            if (e.target.matches('button, .btn, .card-hover, .touch-target')) {
                setTimeout(() => {
                    e.target.style.transform = '';
                }, 100);
            }
        });

        // Prevent double-tap zoom on buttons
        document.addEventListener('touchend', (e) => {
            if (e.target.matches('button, .btn, input[type="submit"]')) {
                e.preventDefault();
                e.target.click();
            }
        });
    }

    setupSwipeGestures() {
        let startX, startY, startTime;

        document.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
            startTime = Date.now();
        });

        document.addEventListener('touchend', (e) => {
            if (!startX || !startY) return;

            const endX = e.changedTouches[0].clientX;
            const endY = e.changedTouches[0].clientY;
            const endTime = Date.now();

            const deltaX = endX - startX;
            const deltaY = endY - startY;
            const deltaTime = endTime - startTime;

            // Only process quick swipes
            if (deltaTime > 300) return;

            const minSwipeDistance = 50;
            const maxVerticalDistance = 100;

            // Horizontal swipe
            if (Math.abs(deltaX) > minSwipeDistance && Math.abs(deltaY) < maxVerticalDistance) {
                if (deltaX > 0) {
                    this.handleSwipeRight();
                } else {
                    this.handleSwipeLeft();
                }
            }

            // Reset
            startX = startY = null;
        });
    }

    handleSwipeRight() {
        // Navigate back or open menu
        if (window.history.length > 1) {
            window.history.back();
        }
    }

    handleSwipeLeft() {
        // Close mobile menu if open
        const mobileMenu = document.getElementById('mobile-menu');
        if (mobileMenu && !mobileMenu.classList.contains('hidden')) {
            mobileMenu.classList.add('hidden');
        }
    }

    setupMobileOptimizations() {
        // Optimize scrolling performance
        document.addEventListener('touchmove', (e) => {
            // Allow scrolling on specific elements
            if (e.target.closest('.overflow-auto, .overflow-y-auto, .swipe-container')) {
                return;
            }
        }, { passive: true });

        // Optimize form inputs for mobile
        const inputs = document.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            // Add mobile-specific attributes
            if (input.type === 'email') {
                input.setAttribute('inputmode', 'email');
            } else if (input.type === 'tel') {
                input.setAttribute('inputmode', 'tel');
            } else if (input.type === 'number') {
                input.setAttribute('inputmode', 'numeric');
            }

            // Prevent zoom on focus for iOS
            if (/iPad|iPhone|iPod/.test(navigator.userAgent)) {
                input.addEventListener('focus', () => {
                    input.style.fontSize = '16px';
                });
            }
        });

        // Handle orientation changes
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                this.handleOrientationChange();
            }, 100);
        });
    }

    setupPullToRefresh() {
        let startY = 0;
        let pullDistance = 0;
        const threshold = 80;
        let isPulling = false;

        document.addEventListener('touchstart', (e) => {
            if (window.scrollY === 0) {
                startY = e.touches[0].clientY;
            }
        });

        document.addEventListener('touchmove', (e) => {
            if (startY === 0) return;

            const currentY = e.touches[0].clientY;
            pullDistance = currentY - startY;

            if (pullDistance > 0 && window.scrollY === 0) {
                isPulling = true;
                e.preventDefault();

                // Visual feedback
                const pullIndicator = this.getPullIndicator();
                if (pullDistance > threshold) {
                    pullIndicator.textContent = '🔄 Release to refresh';
                    pullIndicator.style.color = '#10b981';
                } else {
                    pullIndicator.textContent = '⬇️ Pull to refresh';
                    pullIndicator.style.color = '#6b7280';
                }

                pullIndicator.style.opacity = Math.min(pullDistance / threshold, 1);
                pullIndicator.style.transform = `translateY(${Math.min(pullDistance / 2, 40)}px)`;
            }
        });

        document.addEventListener('touchend', () => {
            if (isPulling && pullDistance > threshold) {
                this.performRefresh();
            }

            // Reset
            startY = 0;
            pullDistance = 0;
            isPulling = false;
            this.hidePullIndicator();
        });
    }

    getPullIndicator() {
        let indicator = document.getElementById('pull-indicator');
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.id = 'pull-indicator';
            indicator.style.cssText = `
                position: fixed;
                top: 0;
                left: 50%;
                transform: translateX(-50%);
                padding: 10px 20px;
                background: rgba(255, 255, 255, 0.9);
                border-radius: 0 0 10px 10px;
                font-size: 14px;
                font-weight: 500;
                opacity: 0;
                transition: opacity 0.3s ease;
                z-index: 1000;
                backdrop-filter: blur(10px);
            `;
            document.body.appendChild(indicator);
        }
        return indicator;
    }

    hidePullIndicator() {
        const indicator = document.getElementById('pull-indicator');
        if (indicator) {
            indicator.style.opacity = '0';
            indicator.style.transform = 'translateX(-50%) translateY(-20px)';
        }
    }

    performRefresh() {
        const indicator = this.getPullIndicator();
        indicator.textContent = '🔄 Refreshing...';
        indicator.style.color = '#10b981';

        setTimeout(() => {
            window.location.reload();
        }, 500);
    }

    setupVirtualKeyboardHandling() {
        // Handle virtual keyboard appearance
        let initialViewportHeight = window.innerHeight;

        window.addEventListener('resize', () => {
            const currentHeight = window.innerHeight;
            const heightDifference = initialViewportHeight - currentHeight;

            if (heightDifference > 150) {
                // Virtual keyboard is likely open
                document.body.classList.add('keyboard-open');
                
                // Scroll active input into view
                const activeElement = document.activeElement;
                if (activeElement && activeElement.tagName === 'INPUT') {
                    setTimeout(() => {
                        activeElement.scrollIntoView({ 
                            behavior: 'smooth', 
                            block: 'center' 
                        });
                    }, 300);
                }
            } else {
                // Virtual keyboard is likely closed
                document.body.classList.remove('keyboard-open');
            }
        });
    }

    handleOrientationChange() {
        // Recalculate viewport dimensions
        const vh = window.innerHeight * 0.01;
        document.documentElement.style.setProperty('--vh', `${vh}px`);

        // Close any open modals or menus
        const mobileMenu = document.getElementById('mobile-menu');
        if (mobileMenu && !mobileMenu.classList.contains('hidden')) {
            mobileMenu.classList.add('hidden');
        }

        // Refresh any charts or dynamic content
        if (window.refreshCharts) {
            window.refreshCharts();
        }
    }

    // Utility methods
    static isMobile() {
        return window.innerWidth <= 767;
    }

    static isTouch() {
        return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    }

    static vibrate(pattern = [100]) {
        if (navigator.vibrate) {
            navigator.vibrate(pattern);
        }
    }
}

// Initialize mobile enhancements when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    if (MobileEnhancements.isMobile() || MobileEnhancements.isTouch()) {
        new MobileEnhancements();
    }

    // Set initial viewport height for mobile browsers
    const vh = window.innerHeight * 0.01;
    document.documentElement.style.setProperty('--vh', `${vh}px`);
});

// Export for use in other scripts
window.MobileEnhancements = MobileEnhancements;
