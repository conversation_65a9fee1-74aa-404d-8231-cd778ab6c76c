<?php
require_once '../config/config.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

requireLogin();

$page_title = 'Реферальная программа';
$user_id = getCurrentUserId();
$user = getUserById($user_id);

// Get user's referral statistics
$db = getDB();

// Get direct referrals
$stmt = $db->prepare("
    SELECT u.username, u.email, u.created_at, u.total_referrals, u.mining_power, u.eco_score,
           COALESCE(SUM(umi.amount), 0) as total_invested
    FROM users u
    LEFT JOIN user_mining_investments umi ON u.id = umi.user_id AND umi.is_active = 1
    WHERE u.referred_by = ?
    GROUP BY u.id
    ORDER BY u.created_at DESC
");
$stmt->execute([$user_id]);
$direct_referrals = $stmt->fetchAll();

// Get referral earnings by level
$stmt = $db->prepare("
    SELECT level, 
           COUNT(*) as count,
           SUM(amount) as total_earned,
           AVG(amount) as avg_earning
    FROM referral_earnings 
    WHERE referrer_id = ?
    GROUP BY level
    ORDER BY level
");
$stmt->execute([$user_id]);
$earnings_by_level = $stmt->fetchAll();

// Get recent referral earnings
$stmt = $db->prepare("
    SELECT re.*, u.username as referred_username
    FROM referral_earnings re
    JOIN users u ON re.referred_id = u.id
    WHERE re.referrer_id = ?
    ORDER BY re.created_at DESC
    LIMIT 20
");
$stmt->execute([$user_id]);
$recent_earnings = $stmt->fetchAll();

// Get referral leaderboard
$stmt = $db->prepare("
    SELECT u.username, u.total_referrals, u.referral_earnings, u.mining_power, u.eco_score,
           RANK() OVER (ORDER BY u.referral_earnings DESC) as rank_position
    FROM users u
    WHERE u.total_referrals > 0
    ORDER BY u.referral_earnings DESC
    LIMIT 50
");
$stmt->execute();
$leaderboard = $stmt->fetchAll();

// Find current user's rank
$user_rank = null;
foreach ($leaderboard as $entry) {
    if ($entry['username'] === $user['username']) {
        $user_rank = $entry['rank_position'];
        break;
    }
}

include '../includes/header.php';
?>

<div class="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">Реферальная программа AstroGenix</h1>
            <p class="text-gray-600">Приглашайте друзей и зарабатывайте до 17% с их инвестиций</p>
        </div>

        <!-- Referral Link Card -->
        <div class="bg-gradient-to-r from-green-500 to-teal-600 rounded-xl p-4 md:p-6 mb-8 text-white referral-link-card">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                <div class="mb-4 lg:mb-0 text-center lg:text-left">
                    <h2 class="text-lg md:text-xl font-bold mb-2">Ваша реферальная ссылка</h2>
                    <p class="text-green-100 text-sm md:text-base">Поделитесь этой ссылкой и получайте комиссию с каждой инвестиции</p>
                </div>
                <div class="flex flex-col space-y-3 lg:flex-row lg:space-y-0 lg:space-x-3">
                    <div class="bg-white bg-opacity-20 backdrop-blur-sm rounded-lg px-3 md:px-4 py-2 font-mono text-xs md:text-sm break-all">
                        <?php echo SITE_URL . '/pages/register.php?ref=' . $user['referral_code']; ?>
                    </div>
                    <button onclick="copyReferralLink()" class="bg-white text-green-600 px-4 py-2 rounded-lg font-semibold hover:bg-green-50 transition-colors touch-target">
                        <i class="fas fa-copy mr-2"></i>Копировать
                    </button>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-8 referral-stats-grid">
            <div class="bg-white rounded-xl p-6 card-shadow border border-gray-100 group hover:shadow-xl transition-all duration-300">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <i class="fas fa-users text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Всего рефералов</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo number_format($user['total_referrals']); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 card-shadow border border-gray-100 group hover:shadow-xl transition-all duration-300">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-teal-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <i class="fas fa-dollar-sign text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Заработано</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo formatCurrency($user['referral_earnings']); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 card-shadow border border-gray-100 group hover:shadow-xl transition-all duration-300">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <i class="fas fa-trophy text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Ваш ранг</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo $user_rank ? '#' . $user_rank : 'N/A'; ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 card-shadow border border-gray-100 group hover:shadow-xl transition-all duration-300">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <i class="fas fa-percentage text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Средняя комиссия</p>
                        <p class="text-2xl font-bold text-gray-900">
                            <?php 
                            $total_earnings = array_sum(array_column($earnings_by_level, 'total_earned'));
                            $total_count = array_sum(array_column($earnings_by_level, 'count'));
                            echo $total_count > 0 ? formatCurrency($total_earnings / $total_count) : '$0';
                            ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Commission Structure -->
        <div class="bg-white rounded-xl card-shadow border border-gray-100 mb-8">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-xl font-bold text-gray-900">Структура комиссий</h2>
                <p class="text-gray-600 mt-1">Многоуровневая система вознаграждений</p>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="text-center p-6 bg-gradient-to-br from-green-50 to-teal-50 rounded-xl border border-green-200">
                        <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-teal-600 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
                            <span class="text-white font-bold text-xl">1</span>
                        </div>
                        <h3 class="font-bold text-gray-900 mb-2">Уровень 1</h3>
                        <p class="text-3xl font-bold text-green-600 mb-2">10%</p>
                        <p class="text-sm text-gray-600">Прямые рефералы</p>
                        <?php 
                        $level1 = array_filter($earnings_by_level, function($e) { return $e['level'] == 1; });
                        $level1 = reset($level1);
                        ?>
                        <div class="mt-3 text-xs text-gray-500">
                            <?php echo $level1 ? $level1['count'] . ' рефералов • ' . formatCurrency($level1['total_earned']) : 'Нет доходов'; ?>
                        </div>
                    </div>

                    <div class="text-center p-6 bg-gradient-to-br from-blue-50 to-purple-50 rounded-xl border border-blue-200">
                        <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
                            <span class="text-white font-bold text-xl">2</span>
                        </div>
                        <h3 class="font-bold text-gray-900 mb-2">Уровень 2</h3>
                        <p class="text-3xl font-bold text-blue-600 mb-2">5%</p>
                        <p class="text-sm text-gray-600">Рефералы рефералов</p>
                        <?php 
                        $level2 = array_filter($earnings_by_level, function($e) { return $e['level'] == 2; });
                        $level2 = reset($level2);
                        ?>
                        <div class="mt-3 text-xs text-gray-500">
                            <?php echo $level2 ? $level2['count'] . ' рефералов • ' . formatCurrency($level2['total_earned']) : 'Нет доходов'; ?>
                        </div>
                    </div>

                    <div class="text-center p-6 bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl border border-purple-200">
                        <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
                            <span class="text-white font-bold text-xl">3</span>
                        </div>
                        <h3 class="font-bold text-gray-900 mb-2">Уровень 3</h3>
                        <p class="text-3xl font-bold text-purple-600 mb-2">2%</p>
                        <p class="text-sm text-gray-600">Третий уровень</p>
                        <?php 
                        $level3 = array_filter($earnings_by_level, function($e) { return $e['level'] == 3; });
                        $level3 = reset($level3);
                        ?>
                        <div class="mt-3 text-xs text-gray-500">
                            <?php echo $level3 ? $level3['count'] . ' рефералов • ' . formatCurrency($level3['total_earned']) : 'Нет доходов'; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Direct Referrals -->
            <div class="bg-white rounded-xl card-shadow border border-gray-100">
                <div class="p-6 border-b border-gray-200">
                    <h2 class="text-xl font-bold text-gray-900">Ваши рефералы</h2>
                    <p class="text-gray-600 mt-1">Пользователи, которых вы пригласили</p>
                </div>
                <div class="p-6">
                    <?php if (empty($direct_referrals)): ?>
                        <div class="text-center py-8">
                            <i class="fas fa-user-plus text-gray-400 text-4xl mb-4"></i>
                            <p class="text-gray-600 mb-4">У вас пока нет рефералов</p>
                            <button onclick="copyReferralLink()" class="bg-gradient-to-r from-green-500 to-teal-600 hover:from-green-600 hover:to-teal-700 text-white px-6 py-2 rounded-lg font-medium transition-all duration-300 transform hover:scale-105 shadow-lg">
                                Поделиться ссылкой
                            </button>
                        </div>
                    <?php else: ?>
                        <div class="space-y-4 max-h-96 overflow-y-auto">
                            <?php foreach ($direct_referrals as $referral): ?>
                                <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-teal-600 rounded-full flex items-center justify-center">
                                            <i class="fas fa-user text-white"></i>
                                        </div>
                                        <div>
                                            <p class="font-semibold text-gray-900"><?php echo htmlspecialchars($referral['username']); ?></p>
                                            <p class="text-sm text-gray-600">Присоединился <?php echo date('d.m.Y', strtotime($referral['created_at'])); ?></p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <p class="font-semibold text-gray-900"><?php echo formatCurrency($referral['total_invested']); ?></p>
                                        <p class="text-sm text-gray-600"><?php echo $referral['total_referrals']; ?> рефералов</p>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Recent Earnings -->
            <div class="bg-white rounded-xl card-shadow border border-gray-100">
                <div class="p-6 border-b border-gray-200">
                    <h2 class="text-xl font-bold text-gray-900">Недавние доходы</h2>
                    <p class="text-gray-600 mt-1">Последние реферальные комиссии</p>
                </div>
                <div class="p-6">
                    <?php if (empty($recent_earnings)): ?>
                        <div class="text-center py-8">
                            <i class="fas fa-coins text-gray-400 text-4xl mb-4"></i>
                            <p class="text-gray-600">Пока нет реферальных доходов</p>
                        </div>
                    <?php else: ?>
                        <div class="space-y-4 max-h-96 overflow-y-auto">
                            <?php foreach ($recent_earnings as $earning): ?>
                                <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-teal-600 rounded-full flex items-center justify-center">
                                            <span class="text-white font-bold text-sm"><?php echo $earning['level']; ?></span>
                                        </div>
                                        <div>
                                            <p class="font-semibold text-gray-900"><?php echo htmlspecialchars($earning['referred_username']); ?></p>
                                            <p class="text-sm text-gray-600">Уровень <?php echo $earning['level']; ?> • <?php echo number_format($earning['commission_rate'] * 100, 1); ?>%</p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <p class="font-semibold text-green-600">+<?php echo formatCurrency($earning['amount']); ?></p>
                                        <p class="text-sm text-gray-600"><?php echo timeAgo($earning['created_at']); ?></p>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Leaderboard -->
        <div class="bg-white rounded-xl card-shadow border border-gray-100 mt-8">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-xl font-bold text-gray-900">Таблица лидеров</h2>
                <p class="text-gray-600 mt-1">Топ рефереров AstroGenix</p>
            </div>
            <div class="p-6">
                <?php if (empty($leaderboard)): ?>
                    <div class="text-center py-8">
                        <i class="fas fa-trophy text-gray-400 text-4xl mb-4"></i>
                        <p class="text-gray-600">Таблица лидеров пока пуста</p>
                    </div>
                <?php else: ?>
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead>
                                <tr class="border-b border-gray-200">
                                    <th class="text-left py-3 px-4 font-semibold text-gray-900">Ранг</th>
                                    <th class="text-left py-3 px-4 font-semibold text-gray-900">Пользователь</th>
                                    <th class="text-left py-3 px-4 font-semibold text-gray-900">Рефералы</th>
                                    <th class="text-left py-3 px-4 font-semibold text-gray-900">Заработано</th>
                                    <th class="text-left py-3 px-4 font-semibold text-gray-900">Майнинг сила</th>
                                    <th class="text-left py-3 px-4 font-semibold text-gray-900">Эко-счет</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach (array_slice($leaderboard, 0, 20) as $index => $leader): ?>
                                    <tr class="border-b border-gray-100 hover:bg-gray-50 transition-colors <?php echo $leader['username'] === $user['username'] ? 'bg-green-50 border-green-200' : ''; ?>">
                                        <td class="py-4 px-4">
                                            <div class="flex items-center">
                                                <?php if ($index < 3): ?>
                                                    <div class="w-8 h-8 rounded-full flex items-center justify-center mr-2 <?php
                                                        echo $index === 0 ? 'bg-yellow-500' : ($index === 1 ? 'bg-gray-400' : 'bg-orange-500');
                                                    ?>">
                                                        <i class="fas fa-trophy text-white text-sm"></i>
                                                    </div>
                                                <?php endif; ?>
                                                <span class="font-semibold text-gray-900">#<?php echo $leader['rank_position']; ?></span>
                                                <?php if ($leader['username'] === $user['username']): ?>
                                                    <span class="ml-2 bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">Вы</span>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td class="py-4 px-4">
                                            <div class="flex items-center">
                                                <div class="w-8 h-8 bg-gradient-to-br from-green-500 to-teal-600 rounded-full flex items-center justify-center mr-3">
                                                    <i class="fas fa-user text-white text-sm"></i>
                                                </div>
                                                <span class="font-medium text-gray-900"><?php echo htmlspecialchars($leader['username']); ?></span>
                                            </div>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="font-semibold text-blue-600"><?php echo number_format($leader['total_referrals']); ?></span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="font-semibold text-green-600"><?php echo formatCurrency($leader['referral_earnings']); ?></span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="font-medium text-purple-600"><?php echo number_format($leader['mining_power'], 2); ?></span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="font-medium text-orange-600"><?php echo number_format($leader['eco_score']); ?></span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <?php if (count($leaderboard) > 20): ?>
                        <div class="mt-4 text-center">
                            <p class="text-gray-600">Показано топ 20 из <?php echo count($leaderboard); ?> участников</p>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script>
function copyReferralLink() {
    const referralLink = '<?php echo SITE_URL . '/pages/register.php?ref=' . $user['referral_code']; ?>';

    if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(referralLink).then(() => {
            showNotification('Реферальная ссылка скопирована!', 'success');
        }).catch(() => {
            fallbackCopyTextToClipboard(referralLink);
        });
    } else {
        fallbackCopyTextToClipboard(referralLink);
    }
}

function fallbackCopyTextToClipboard(text) {
    const textArea = document.createElement("textarea");
    textArea.value = text;
    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.position = "fixed";

    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        document.execCommand('copy');
        showNotification('Реферальная ссылка скопирована!', 'success');
    } catch (err) {
        showNotification('Не удалось скопировать ссылку', 'error');
    }

    document.body.removeChild(textArea);
}

function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        'bg-blue-500 text-white'
    }`;
    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas ${type === 'success' ? 'fa-check' : type === 'error' ? 'fa-times' : 'fa-info'} mr-2"></i>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    // Animate out and remove
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Add eco-mining animations
document.addEventListener('DOMContentLoaded', function() {
    // Animate statistics cards on scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe all cards
    document.querySelectorAll('.card-shadow').forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(card);
    });
});
</script>

<?php include '../includes/footer.php'; ?>
