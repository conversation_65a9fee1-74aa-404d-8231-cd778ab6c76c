# 🌱 AstroGenix - Руководство по установке

## Обзор

AstroGenix - это современная платформа экологического майнинга криптовалют, построенная на PHP/MySQL с полной мобильной оптимизацией и интерактивными анимациями.

## 🚀 Быстрая установка

### Автоматическая установка (Рекомендуется)

1. **Загрузите файлы** на ваш веб-сервер
2. **Откройте браузер** и перейдите по адресу: `http://ваш-домен.com/install/`
3. **Следуйте мастеру установки** - он автоматически:
   - Проверит системные требования
   - Настроит базу данных
   - Создаст администратора
   - Установит все необходимые файлы

### Ручная установка

Если автоматическая установка недоступна, следуйте инструкциям ниже.

## 📋 Системные требования

### Обязательные требования

- **PHP**: 7.4 или выше (рекомендуется 8.0+)
- **MySQL/MariaDB**: 5.7+ / 10.2+
- **Веб-сервер**: Apache 2.4+ или Nginx 1.18+
- **SSL-сертификат**: Обязательно для production

### PHP Расширения (обязательные)

- `pdo` - для работы с базой данных
- `pdo_mysql` - MySQL драйвер
- `mbstring` - поддержка многобайтовых строк
- `openssl` - шифрование и безопасность
- `json` - обработка JSON данных
- `curl` - HTTP запросы

### PHP Расширения (рекомендуемые)

- `gd` - обработка изображений
- `zip` - работа с архивами
- `fileinfo` - определение типов файлов

### Настройки PHP

```ini
memory_limit = 128M
max_execution_time = 300
upload_max_filesize = 10M
post_max_size = 10M
```

## 🗂️ Структура проекта

```
astrogenix/
├── admin/                  # Административная панель
├── assets/                 # CSS, JS, изображения
│   ├── css/               # Стили
│   ├── js/                # JavaScript
│   └── images/            # Изображения
├── config/                # Конфигурационные файлы
├── database/              # SQL схемы и миграции
├── includes/              # Общие PHP файлы
├── install/               # Мастер установки
├── logs/                  # Логи системы
├── pages/                 # Страницы пользователей
├── scripts/               # Утилиты и скрипты
├── test/                  # Тестирование
└── uploads/               # Загруженные файлы
```

## 🔧 Ручная установка

### Шаг 1: Подготовка сервера

1. **Создайте базу данных**:
```sql
CREATE DATABASE astrogenix CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. **Создайте пользователя БД**:
```sql
CREATE USER 'astrogenix_user'@'localhost' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON astrogenix.* TO 'astrogenix_user'@'localhost';
FLUSH PRIVILEGES;
```

3. **Настройте права доступа**:
```bash
chmod 755 config/ logs/ uploads/ cache/
chmod 644 config/config.php
```

### Шаг 2: Импорт базы данных

```bash
mysql -u astrogenix_user -p astrogenix < database/install.sql
```

### Шаг 3: Конфигурация

1. **Скопируйте файл конфигурации**:
```bash
cp config/config.example.php config/config.php
```

2. **Отредактируйте настройки**:
```php
// Database Configuration
define('DB_HOST', 'localhost');
define('DB_PORT', '3306');
define('DB_NAME', 'astrogenix');
define('DB_USER', 'astrogenix_user');
define('DB_PASS', 'secure_password');

// Site Configuration
define('SITE_URL', 'https://ваш-домен.com');
define('ADMIN_EMAIL', 'admin@ваш-домен.com');
```

### Шаг 4: Настройка веб-сервера

#### Apache (.htaccess)

```apache
RewriteEngine On
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Security Headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"

# Cache Control
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
</IfModule>
```

#### Nginx

```nginx
server {
    listen 80;
    server_name ваш-домен.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name ваш-домен.com;
    root /path/to/astrogenix;
    index index.php;

    # SSL Configuration
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;

    # Security Headers
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.0-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # Cache static files
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Deny access to sensitive files
    location ~ /\.(ht|git) {
        deny all;
    }

    location ~ /(config|logs|database|install)/ {
        deny all;
    }
}
```

## 🔐 Безопасность

### После установки

1. **Удалите папку install/**:
```bash
rm -rf install/
```

2. **Измените пароли по умолчанию**:
   - Администратор: `admin` / `admin123`
   - Демо-пользователи: `password123`

3. **Настройте SSL-сертификат**:
   - Используйте Let's Encrypt или коммерческий сертификат
   - Принудительно перенаправляйте HTTP на HTTPS

4. **Обновите настройки безопасности**:
```php
// В config/config.php
define('SECRET_KEY', 'ваш-уникальный-ключ-64-символа');
define('ENCRYPTION_KEY', 'ваш-ключ-шифрования-32-символа');
```

### Рекомендации по безопасности

- Регулярно обновляйте PHP и MySQL
- Используйте сильные пароли
- Настройте файрвол
- Включите логирование
- Регулярно создавайте резервные копии

## ⚙️ Настройка после установки

### 1. Административная панель

Войдите в админ-панель: `https://ваш-домен.com/admin/`

**Основные настройки:**
- Обновите информацию о сайте
- Настройте SMTP для email
- Добавьте реальные майнинг-планы
- Настройте платежные системы

### 2. Email настройки

В админ-панели настройте SMTP:
```
SMTP Host: smtp.gmail.com
SMTP Port: 587
SMTP Username: ваш<EMAIL>
SMTP Password: ваш-пароль-приложения
```

### 3. Демо-данные

Система включает демо-данные:
- 5 демо-пользователей
- 6 майнинг-планов
- 8 базовых заданий
- Примеры транзакций

**Удаление демо-данных:**
```sql
-- Удалить демо-пользователей (кроме admin)
DELETE FROM users WHERE id > 1;

-- Сбросить автоинкремент
ALTER TABLE users AUTO_INCREMENT = 2;
```

## 🧪 Тестирование

### Автоматическое тестирование

Запустите системный тест:
```
https://ваш-домен.com/test/system-test.php
```

Тест проверяет:
- Подключение к базе данных
- Функции пользователей
- Майнинг-планы
- Реферальную систему
- Систему заданий
- Мобильную оптимизацию
- Анимации
- Безопасность

### Ручное тестирование

1. **Регистрация пользователя**
2. **Вход в систему**
3. **Создание инвестиции**
4. **Проверка реферальной системы**
5. **Выполнение заданий**
6. **Мобильная версия**

## 📱 Мобильная оптимизация

Платформа полностью оптимизирована для мобильных устройств:

- **Responsive дизайн**: 320px - 1920px+
- **Touch-friendly интерфейс**: Минимум 44px для кнопок
- **Swipe навигация**: Жесты влево/вправо
- **Pull-to-refresh**: Обновление контента
- **Виртуальная клавиатура**: Автоматическая адаптация
- **Floating Action Button**: Быстрый доступ к функциям

## 🎨 Кастомизация

### Цветовая схема

Основные цвета в `assets/css/style.css`:
```css
:root {
    --primary-color: #10b981;    /* Зеленый */
    --primary-dark: #059669;
    --secondary-color: #8b5cf6;  /* Фиолетовый */
    --secondary-dark: #7c3aed;
}
```

### Анимации

Эко-анимации в `assets/css/animations.css`:
- Пульсация листьев
- Поток энергии
- Прогресс майнинга
- Частицы
- Солнечные лучи

## 🔧 Обслуживание

### Резервное копирование

**База данных:**
```bash
mysqldump -u username -p astrogenix > backup_$(date +%Y%m%d).sql
```

**Файлы:**
```bash
tar -czf astrogenix_backup_$(date +%Y%m%d).tar.gz /path/to/astrogenix/
```

### Логи

Проверяйте логи в папке `logs/`:
- `error.log` - ошибки PHP
- `access.log` - доступ к сайту
- `security.log` - события безопасности

### Обновления

1. Создайте резервную копию
2. Загрузите новые файлы
3. Запустите миграции БД (если есть)
4. Очистите кэш
5. Протестируйте функциональность

## 🆘 Устранение неполадок

### Частые проблемы

**1. Ошибка подключения к БД**
- Проверьте настройки в `config/config.php`
- Убедитесь, что MySQL запущен
- Проверьте права пользователя БД

**2. Ошибки прав доступа**
```bash
chmod 755 logs/ uploads/ cache/
chown www-data:www-data logs/ uploads/ cache/
```

**3. Проблемы с SSL**
- Проверьте сертификат
- Обновите настройки веб-сервера
- Принудительно используйте HTTPS

**4. Медленная загрузка**
- Включите сжатие GZIP
- Оптимизируйте изображения
- Настройте кэширование
- Используйте CDN

### Логи отладки

Включите отладку в `config/config.php`:
```php
define('DEBUG_MODE', true);
define('ENVIRONMENT', 'development');
```

## 📞 Поддержка

- **Документация**: `/PRODUCTION-README.md`
- **Тестирование**: `/test/system-test.php`
- **Логи**: `/logs/`
- **GitHub**: [ссылка на репозиторий]

## 📄 Лицензия

AstroGenix распространяется под лицензией MIT. См. файл LICENSE для подробностей.
