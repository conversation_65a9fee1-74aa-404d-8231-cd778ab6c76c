<?php
/**
 * AstroGenix Installation Wizard
 * Automated installation script for the eco-mining platform
 */

session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

class AstroGenixInstaller {
    private $steps = [
        'welcome' => 'Добро пожаловать',
        'requirements' => 'Системные требования',
        'database' => 'Настройка базы данных',
        'admin' => 'Администратор',
        'install' => 'Установка',
        'complete' => 'Завершение'
    ];
    
    private $currentStep;
    private $errors = [];
    private $warnings = [];
    
    public function __construct() {
        $this->currentStep = $_GET['step'] ?? 'welcome';
        
        // Проверка безопасности - удалить после установки
        if (file_exists('../config/config.php') && $this->currentStep !== 'complete') {
            $this->redirect('complete');
        }
    }
    
    public function run() {
        $this->renderHeader();
        
        switch ($this->currentStep) {
            case 'welcome':
                $this->stepWelcome();
                break;
            case 'requirements':
                $this->stepRequirements();
                break;
            case 'database':
                $this->stepDatabase();
                break;
            case 'admin':
                $this->stepAdmin();
                break;
            case 'install':
                $this->stepInstall();
                break;
            case 'complete':
                $this->stepComplete();
                break;
            default:
                $this->redirect('welcome');
        }
        
        $this->renderFooter();
    }
    
    private function stepWelcome() {
        ?>
        <div class="step-content">
            <div class="welcome-hero">
                <div class="eco-icon">🌱</div>
                <h1>Добро пожаловать в AstroGenix</h1>
                <p class="lead">Платформа экологического майнинга криптовалют</p>
            </div>
            
            <div class="features-grid">
                <div class="feature-card">
                    <i class="fas fa-leaf"></i>
                    <h3>Эко-майнинг</h3>
                    <p>100% возобновляемая энергия</p>
                </div>
                <div class="feature-card">
                    <i class="fas fa-users"></i>
                    <h3>Реферальная система</h3>
                    <p>3-уровневая программа</p>
                </div>
                <div class="feature-card">
                    <i class="fas fa-tasks"></i>
                    <h3>Система заданий</h3>
                    <p>USDT награды за активность</p>
                </div>
                <div class="feature-card">
                    <i class="fas fa-mobile-alt"></i>
                    <h3>Мобильная версия</h3>
                    <p>Полная адаптация</p>
                </div>
            </div>
            
            <div class="installation-info">
                <h3>Что будет установлено:</h3>
                <ul>
                    <li>✅ Полная база данных с демо-данными</li>
                    <li>✅ Административная панель</li>
                    <li>✅ Система пользователей и инвестиций</li>
                    <li>✅ Реферальная программа</li>
                    <li>✅ Система заданий и наград</li>
                    <li>✅ Мобильная оптимизация</li>
                    <li>✅ Безопасность и производительность</li>
                </ul>
            </div>
            
            <div class="step-actions">
                <a href="?step=requirements" class="btn btn-primary btn-lg">
                    <i class="fas fa-arrow-right"></i>
                    Начать установку
                </a>
            </div>
        </div>
        <?php
    }
    
    private function stepRequirements() {
        $requirements = $this->checkRequirements();
        $canProceed = empty($requirements['errors']);
        
        ?>
        <div class="step-content">
            <h2>Проверка системных требований</h2>
            
            <div class="requirements-list">
                <?php foreach ($requirements['checks'] as $check): ?>
                <div class="requirement-item <?php echo $check['status']; ?>">
                    <div class="requirement-icon">
                        <?php if ($check['status'] === 'success'): ?>
                            <i class="fas fa-check-circle text-success"></i>
                        <?php elseif ($check['status'] === 'warning'): ?>
                            <i class="fas fa-exclamation-triangle text-warning"></i>
                        <?php else: ?>
                            <i class="fas fa-times-circle text-danger"></i>
                        <?php endif; ?>
                    </div>
                    <div class="requirement-details">
                        <h4><?php echo $check['name']; ?></h4>
                        <p><?php echo $check['message']; ?></p>
                        <?php if (isset($check['current'])): ?>
                            <small>Текущее значение: <?php echo $check['current']; ?></small>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            
            <?php if (!empty($requirements['errors'])): ?>
                <div class="alert alert-danger">
                    <h4>Критические ошибки:</h4>
                    <ul>
                        <?php foreach ($requirements['errors'] as $error): ?>
                            <li><?php echo $error; ?></li>
                        <?php endforeach; ?>
                    </ul>
                    <p>Пожалуйста, исправьте эти проблемы перед продолжением установки.</p>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($requirements['warnings'])): ?>
                <div class="alert alert-warning">
                    <h4>Предупреждения:</h4>
                    <ul>
                        <?php foreach ($requirements['warnings'] as $warning): ?>
                            <li><?php echo $warning; ?></li>
                        <?php endforeach; ?>
                    </ul>
                    <p>Эти проблемы не критичны, но рекомендуется их исправить.</p>
                </div>
            <?php endif; ?>
            
            <div class="step-actions">
                <a href="?step=welcome" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i>
                    Назад
                </a>
                
                <?php if ($canProceed): ?>
                    <a href="?step=database" class="btn btn-primary">
                        <i class="fas fa-arrow-right"></i>
                        Продолжить
                    </a>
                <?php else: ?>
                    <button class="btn btn-primary" disabled>
                        Исправьте ошибки для продолжения
                    </button>
                <?php endif; ?>
            </div>
        </div>
        <?php
    }
    
    private function stepDatabase() {
        if ($_POST) {
            $dbConfig = $this->validateDatabaseConfig($_POST);
            if ($dbConfig) {
                $_SESSION['db_config'] = $dbConfig;
                $this->redirect('admin');
            }
        }
        
        ?>
        <div class="step-content">
            <h2>Настройка базы данных</h2>
            <p>Введите параметры подключения к базе данных MySQL/MariaDB:</p>
            
            <?php if (!empty($this->errors)): ?>
                <div class="alert alert-danger">
                    <ul>
                        <?php foreach ($this->errors as $error): ?>
                            <li><?php echo $error; ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
            
            <form method="POST" class="database-form">
                <div class="form-group">
                    <label for="db_host">Хост базы данных:</label>
                    <input type="text" id="db_host" name="db_host" value="<?php echo $_POST['db_host'] ?? 'localhost'; ?>" required>
                    <small>Обычно localhost или IP-адрес сервера</small>
                </div>
                
                <div class="form-group">
                    <label for="db_port">Порт:</label>
                    <input type="number" id="db_port" name="db_port" value="<?php echo $_POST['db_port'] ?? '3306'; ?>" required>
                    <small>Стандартный порт MySQL: 3306</small>
                </div>
                
                <div class="form-group">
                    <label for="db_name">Имя базы данных:</label>
                    <input type="text" id="db_name" name="db_name" value="<?php echo $_POST['db_name'] ?? 'astrogenix'; ?>" required>
                    <small>База данных будет создана автоматически</small>
                </div>
                
                <div class="form-group">
                    <label for="db_user">Пользователь:</label>
                    <input type="text" id="db_user" name="db_user" value="<?php echo $_POST['db_user'] ?? ''; ?>" required>
                    <small>Пользователь с правами создания БД</small>
                </div>
                
                <div class="form-group">
                    <label for="db_pass">Пароль:</label>
                    <input type="password" id="db_pass" name="db_pass" value="<?php echo $_POST['db_pass'] ?? ''; ?>">
                    <small>Пароль пользователя базы данных</small>
                </div>
                
                <div class="form-group">
                    <label for="db_prefix">Префикс таблиц:</label>
                    <input type="text" id="db_prefix" name="db_prefix" value="<?php echo $_POST['db_prefix'] ?? ''; ?>">
                    <small>Опционально: префикс для таблиц (например: ag_)</small>
                </div>
                
                <div class="step-actions">
                    <a href="?step=requirements" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i>
                        Назад
                    </a>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-database"></i>
                        Проверить подключение
                    </button>
                </div>
            </form>
        </div>
        <?php
    }
    
    private function stepAdmin() {
        if ($_POST) {
            $adminConfig = $this->validateAdminConfig($_POST);
            if ($adminConfig) {
                $_SESSION['admin_config'] = $adminConfig;
                $this->redirect('install');
            }
        }
        
        ?>
        <div class="step-content">
            <h2>Создание администратора</h2>
            <p>Создайте учетную запись главного администратора платформы:</p>
            
            <?php if (!empty($this->errors)): ?>
                <div class="alert alert-danger">
                    <ul>
                        <?php foreach ($this->errors as $error): ?>
                            <li><?php echo $error; ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
            
            <form method="POST" class="admin-form">
                <div class="form-group">
                    <label for="admin_username">Имя пользователя:</label>
                    <input type="text" id="admin_username" name="admin_username" value="<?php echo $_POST['admin_username'] ?? 'admin'; ?>" required>
                    <small>Логин для входа в админ-панель</small>
                </div>
                
                <div class="form-group">
                    <label for="admin_email">Email:</label>
                    <input type="email" id="admin_email" name="admin_email" value="<?php echo $_POST['admin_email'] ?? ''; ?>" required>
                    <small>Email администратора</small>
                </div>
                
                <div class="form-group">
                    <label for="admin_password">Пароль:</label>
                    <input type="password" id="admin_password" name="admin_password" required>
                    <small>Минимум 8 символов</small>
                </div>
                
                <div class="form-group">
                    <label for="admin_password_confirm">Подтверждение пароля:</label>
                    <input type="password" id="admin_password_confirm" name="admin_password_confirm" required>
                    <small>Повторите пароль</small>
                </div>
                
                <div class="form-group">
                    <label for="site_url">URL сайта:</label>
                    <input type="url" id="site_url" name="site_url" value="<?php echo $_POST['site_url'] ?? $this->getCurrentUrl(); ?>" required>
                    <small>Полный URL вашего сайта</small>
                </div>
                
                <div class="form-group">
                    <label for="site_email">Email поддержки:</label>
                    <input type="email" id="site_email" name="site_email" value="<?php echo $_POST['site_email'] ?? ''; ?>" required>
                    <small>Email для связи с поддержкой</small>
                </div>
                
                <div class="step-actions">
                    <a href="?step=database" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i>
                        Назад
                    </a>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-arrow-right"></i>
                        Продолжить
                    </button>
                </div>
            </form>
        </div>
        <?php
    }
    
    private function stepInstall() {
        if (!isset($_SESSION['db_config']) || !isset($_SESSION['admin_config'])) {
            $this->redirect('database');
        }
        
        $installResult = $this->performInstallation();
        
        ?>
        <div class="step-content">
            <h2>Процесс установки</h2>
            
            <div class="installation-progress">
                <?php foreach ($installResult['steps'] as $step): ?>
                <div class="install-step <?php echo $step['status']; ?>">
                    <div class="step-icon">
                        <?php if ($step['status'] === 'success'): ?>
                            <i class="fas fa-check-circle text-success"></i>
                        <?php elseif ($step['status'] === 'error'): ?>
                            <i class="fas fa-times-circle text-danger"></i>
                        <?php else: ?>
                            <i class="fas fa-clock text-warning"></i>
                        <?php endif; ?>
                    </div>
                    <div class="step-details">
                        <h4><?php echo $step['name']; ?></h4>
                        <p><?php echo $step['message']; ?></p>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            
            <?php if ($installResult['success']): ?>
                <div class="alert alert-success">
                    <h4>🎉 Установка завершена успешно!</h4>
                    <p>AstroGenix готов к использованию.</p>
                </div>
                
                <div class="step-actions">
                    <a href="?step=complete" class="btn btn-primary btn-lg">
                        <i class="fas fa-rocket"></i>
                        Завершить установку
                    </a>
                </div>
            <?php else: ?>
                <div class="alert alert-danger">
                    <h4>Ошибка установки</h4>
                    <p>Произошли ошибки во время установки. Проверьте логи и попробуйте снова.</p>
                </div>
                
                <div class="step-actions">
                    <a href="?step=database" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i>
                        Попробовать снова
                    </a>
                </div>
            <?php endif; ?>
        </div>
        <?php
    }
    
    private function stepComplete() {
        // Очистка сессии
        session_destroy();
        
        ?>
        <div class="step-content">
            <div class="completion-hero">
                <div class="success-icon">🚀</div>
                <h1>Установка завершена!</h1>
                <p class="lead">AstroGenix успешно установлен и готов к использованию</p>
            </div>
            
            <div class="completion-info">
                <div class="info-card">
                    <h3>🔐 Безопасность</h3>
                    <ul>
                        <li>Удалите папку <code>/install</code> для безопасности</li>
                        <li>Измените пароли по умолчанию</li>
                        <li>Настройте SSL-сертификат</li>
                        <li>Проверьте права доступа к файлам</li>
                    </ul>
                </div>
                
                <div class="info-card">
                    <h3>⚙️ Настройка</h3>
                    <ul>
                        <li>Настройте SMTP для отправки email</li>
                        <li>Обновите настройки в админ-панели</li>
                        <li>Добавьте реальные майнинг-планы</li>
                        <li>Настройте платежные системы</li>
                    </ul>
                </div>
                
                <div class="info-card">
                    <h3>📊 Демо-данные</h3>
                    <ul>
                        <li>Созданы демо-пользователи</li>
                        <li>Добавлены примеры майнинг-планов</li>
                        <li>Настроены базовые задания</li>
                        <li>Можно удалить через админ-панель</li>
                    </ul>
                </div>
            </div>
            
            <div class="quick-links">
                <a href="../index.php" class="btn btn-primary btn-lg">
                    <i class="fas fa-home"></i>
                    Главная страница
                </a>
                
                <a href="../admin/" class="btn btn-success btn-lg">
                    <i class="fas fa-cog"></i>
                    Админ-панель
                </a>
                
                <a href="../pages/login.php" class="btn btn-info btn-lg">
                    <i class="fas fa-sign-in-alt"></i>
                    Вход в систему
                </a>
            </div>
            
            <div class="support-info">
                <h3>Нужна помощь?</h3>
                <p>
                    Документация: <code>/PRODUCTION-README.md</code><br>
                    Тестирование: <code>/test/system-test.php</code><br>
                    Логи: <code>/logs/</code>
                </p>
            </div>
        </div>
        <?php
    }

    private function checkRequirements() {
        $checks = [];
        $errors = [];
        $warnings = [];

        // PHP Version
        $phpVersion = PHP_VERSION;
        $minPhpVersion = '7.4.0';
        if (version_compare($phpVersion, $minPhpVersion, '>=')) {
            $checks[] = [
                'name' => 'PHP Version',
                'status' => 'success',
                'message' => 'PHP версия совместима',
                'current' => $phpVersion
            ];
        } else {
            $checks[] = [
                'name' => 'PHP Version',
                'status' => 'error',
                'message' => "Требуется PHP $minPhpVersion или выше",
                'current' => $phpVersion
            ];
            $errors[] = "PHP версия $phpVersion не поддерживается. Требуется $minPhpVersion или выше.";
        }

        // Required PHP Extensions
        $requiredExtensions = ['pdo', 'pdo_mysql', 'mbstring', 'openssl', 'json', 'curl'];
        foreach ($requiredExtensions as $ext) {
            if (extension_loaded($ext)) {
                $checks[] = [
                    'name' => "PHP Extension: $ext",
                    'status' => 'success',
                    'message' => 'Расширение установлено'
                ];
            } else {
                $checks[] = [
                    'name' => "PHP Extension: $ext",
                    'status' => 'error',
                    'message' => 'Расширение не найдено'
                ];
                $errors[] = "Требуется PHP расширение: $ext";
            }
        }

        // Optional PHP Extensions
        $optionalExtensions = ['gd', 'zip', 'fileinfo'];
        foreach ($optionalExtensions as $ext) {
            if (extension_loaded($ext)) {
                $checks[] = [
                    'name' => "PHP Extension: $ext (optional)",
                    'status' => 'success',
                    'message' => 'Расширение установлено'
                ];
            } else {
                $checks[] = [
                    'name' => "PHP Extension: $ext (optional)",
                    'status' => 'warning',
                    'message' => 'Рекомендуется установить'
                ];
                $warnings[] = "Рекомендуется установить PHP расширение: $ext";
            }
        }

        // File Permissions
        $writableDirs = ['../config', '../logs', '../uploads', '../cache'];
        foreach ($writableDirs as $dir) {
            if (!is_dir($dir)) {
                @mkdir($dir, 0755, true);
            }

            if (is_writable($dir)) {
                $checks[] = [
                    'name' => "Writable: $dir",
                    'status' => 'success',
                    'message' => 'Директория доступна для записи'
                ];
            } else {
                $checks[] = [
                    'name' => "Writable: $dir",
                    'status' => 'error',
                    'message' => 'Нет прав на запись'
                ];
                $errors[] = "Нет прав на запись в директорию: $dir";
            }
        }

        // Memory Limit
        $memoryLimit = ini_get('memory_limit');
        $memoryBytes = $this->parseMemoryLimit($memoryLimit);
        $minMemory = 128 * 1024 * 1024; // 128MB

        if ($memoryBytes >= $minMemory || $memoryBytes === -1) {
            $checks[] = [
                'name' => 'Memory Limit',
                'status' => 'success',
                'message' => 'Достаточно памяти',
                'current' => $memoryLimit
            ];
        } else {
            $checks[] = [
                'name' => 'Memory Limit',
                'status' => 'warning',
                'message' => 'Рекомендуется увеличить до 128M',
                'current' => $memoryLimit
            ];
            $warnings[] = "Рекомендуется увеличить memory_limit до 128M (текущее: $memoryLimit)";
        }

        return [
            'checks' => $checks,
            'errors' => $errors,
            'warnings' => $warnings
        ];
    }

    private function parseMemoryLimit($limit) {
        if ($limit === '-1') return -1;

        $limit = trim($limit);
        $last = strtolower($limit[strlen($limit)-1]);
        $limit = (int) $limit;

        switch($last) {
            case 'g': $limit *= 1024;
            case 'm': $limit *= 1024;
            case 'k': $limit *= 1024;
        }

        return $limit;
    }

    private function validateDatabaseConfig($data) {
        $this->errors = [];

        $required = ['db_host', 'db_port', 'db_name', 'db_user'];
        foreach ($required as $field) {
            if (empty($data[$field])) {
                $this->errors[] = "Поле '$field' обязательно для заполнения";
            }
        }

        if (!empty($this->errors)) {
            return false;
        }

        // Test database connection
        try {
            $dsn = "mysql:host={$data['db_host']};port={$data['db_port']};charset=utf8mb4";
            $pdo = new PDO($dsn, $data['db_user'], $data['db_pass'], [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
            ]);

            // Try to create database if it doesn't exist
            $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$data['db_name']}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");

            return $data;
        } catch (PDOException $e) {
            $this->errors[] = "Ошибка подключения к базе данных: " . $e->getMessage();
            return false;
        }
    }

    private function validateAdminConfig($data) {
        $this->errors = [];

        $required = ['admin_username', 'admin_email', 'admin_password', 'admin_password_confirm', 'site_url', 'site_email'];
        foreach ($required as $field) {
            if (empty($data[$field])) {
                $this->errors[] = "Поле '$field' обязательно для заполнения";
            }
        }

        if ($data['admin_password'] !== $data['admin_password_confirm']) {
            $this->errors[] = "Пароли не совпадают";
        }

        if (strlen($data['admin_password']) < 8) {
            $this->errors[] = "Пароль должен содержать минимум 8 символов";
        }

        if (!filter_var($data['admin_email'], FILTER_VALIDATE_EMAIL)) {
            $this->errors[] = "Некорректный email администратора";
        }

        if (!filter_var($data['site_email'], FILTER_VALIDATE_EMAIL)) {
            $this->errors[] = "Некорректный email поддержки";
        }

        if (!filter_var($data['site_url'], FILTER_VALIDATE_URL)) {
            $this->errors[] = "Некорректный URL сайта";
        }

        return empty($this->errors) ? $data : false;
    }

    private function performInstallation() {
        $steps = [];
        $success = true;

        try {
            // Step 1: Create database connection
            $steps[] = [
                'name' => 'Подключение к базе данных',
                'status' => 'processing',
                'message' => 'Подключение к MySQL серверу...'
            ];

            $dbConfig = $_SESSION['db_config'];
            $dsn = "mysql:host={$dbConfig['db_host']};port={$dbConfig['db_port']};dbname={$dbConfig['db_name']};charset=utf8mb4";
            $pdo = new PDO($dsn, $dbConfig['db_user'], $dbConfig['db_pass'], [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
            ]);

            $steps[0]['status'] = 'success';
            $steps[0]['message'] = 'Подключение установлено';

            // Step 2: Import database schema
            $steps[] = [
                'name' => 'Создание структуры базы данных',
                'status' => 'processing',
                'message' => 'Импорт SQL схемы...'
            ];

            $sqlFile = '../database/install.sql';
            if (!file_exists($sqlFile)) {
                throw new Exception("Файл SQL схемы не найден: $sqlFile");
            }

            $sql = file_get_contents($sqlFile);
            $pdo->exec($sql);

            $steps[1]['status'] = 'success';
            $steps[1]['message'] = 'База данных создана';

            // Step 3: Create admin user
            $steps[] = [
                'name' => 'Создание администратора',
                'status' => 'processing',
                'message' => 'Создание учетной записи администратора...'
            ];

            $adminConfig = $_SESSION['admin_config'];
            $hashedPassword = password_hash($adminConfig['admin_password'], PASSWORD_DEFAULT);

            $stmt = $pdo->prepare("UPDATE users SET username = ?, email = ?, password = ? WHERE id = 1");
            $stmt->execute([
                $adminConfig['admin_username'],
                $adminConfig['admin_email'],
                $hashedPassword
            ]);

            $steps[2]['status'] = 'success';
            $steps[2]['message'] = 'Администратор создан';

            // Step 4: Create configuration file
            $steps[] = [
                'name' => 'Создание конфигурации',
                'status' => 'processing',
                'message' => 'Создание файла конфигурации...'
            ];

            $this->createConfigFile($dbConfig, $adminConfig);

            $steps[3]['status'] = 'success';
            $steps[3]['message'] = 'Конфигурация создана';

            // Step 5: Set permissions
            $steps[] = [
                'name' => 'Настройка прав доступа',
                'status' => 'processing',
                'message' => 'Установка прав доступа к файлам...'
            ];

            $this->setFilePermissions();

            $steps[4]['status'] = 'success';
            $steps[4]['message'] = 'Права доступа настроены';

            // Step 6: Final verification
            $steps[] = [
                'name' => 'Финальная проверка',
                'status' => 'processing',
                'message' => 'Проверка установки...'
            ];

            // Test if we can connect with new config
            require_once('../config/config.php');
            $testPdo = getDBConnection();
            $testResult = $testPdo->query("SELECT COUNT(*) FROM users")->fetchColumn();

            if ($testResult > 0) {
                $steps[5]['status'] = 'success';
                $steps[5]['message'] = 'Установка проверена и готова';
            } else {
                throw new Exception("Проверка установки не пройдена");
            }

        } catch (Exception $e) {
            $success = false;
            $lastStep = count($steps) - 1;
            if ($lastStep >= 0) {
                $steps[$lastStep]['status'] = 'error';
                $steps[$lastStep]['message'] = 'Ошибка: ' . $e->getMessage();
            }
        }

        return [
            'success' => $success,
            'steps' => $steps
        ];
    }

    private function createConfigFile($dbConfig, $adminConfig) {
        $configContent = "<?php\n";
        $configContent .= "/**\n";
        $configContent .= " * AstroGenix Configuration File\n";
        $configContent .= " * Generated by installation wizard\n";
        $configContent .= " */\n\n";

        $configContent .= "// Database Configuration\n";
        $configContent .= "define('DB_HOST', '{$dbConfig['db_host']}');\n";
        $configContent .= "define('DB_PORT', '{$dbConfig['db_port']}');\n";
        $configContent .= "define('DB_NAME', '{$dbConfig['db_name']}');\n";
        $configContent .= "define('DB_USER', '{$dbConfig['db_user']}');\n";
        $configContent .= "define('DB_PASS', '{$dbConfig['db_pass']}');\n";
        $configContent .= "define('DB_PREFIX', '{$dbConfig['db_prefix']}');\n\n";

        $configContent .= "// Site Configuration\n";
        $configContent .= "define('SITE_URL', '{$adminConfig['site_url']}');\n";
        $configContent .= "define('SITE_NAME', 'AstroGenix');\n";
        $configContent .= "define('ADMIN_EMAIL', '{$adminConfig['admin_email']}');\n";
        $configContent .= "define('SUPPORT_EMAIL', '{$adminConfig['site_email']}');\n\n";

        $configContent .= "// Security Configuration\n";
        $configContent .= "define('SECRET_KEY', '" . bin2hex(random_bytes(32)) . "');\n";
        $configContent .= "define('ENCRYPTION_KEY', '" . bin2hex(random_bytes(16)) . "');\n";
        $configContent .= "define('SESSION_LIFETIME', 3600);\n\n";

        $configContent .= "// Environment\n";
        $configContent .= "define('ENVIRONMENT', 'production');\n";
        $configContent .= "define('DEBUG_MODE', false);\n\n";

        $configContent .= "// Database Connection Function\n";
        $configContent .= "function getDBConnection() {\n";
        $configContent .= "    static \$pdo = null;\n";
        $configContent .= "    if (\$pdo === null) {\n";
        $configContent .= "        \$dsn = 'mysql:host=' . DB_HOST . ';port=' . DB_PORT . ';dbname=' . DB_NAME . ';charset=utf8mb4';\n";
        $configContent .= "        \$options = [\n";
        $configContent .= "            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,\n";
        $configContent .= "            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,\n";
        $configContent .= "            PDO::ATTR_EMULATE_PREPARES => false\n";
        $configContent .= "        ];\n";
        $configContent .= "        \$pdo = new PDO(\$dsn, DB_USER, DB_PASS, \$options);\n";
        $configContent .= "    }\n";
        $configContent .= "    return \$pdo;\n";
        $configContent .= "}\n\n";

        $configContent .= "// Timezone\n";
        $configContent .= "date_default_timezone_set('UTC');\n\n";

        $configContent .= "// Error Reporting\n";
        $configContent .= "if (ENVIRONMENT === 'development') {\n";
        $configContent .= "    error_reporting(E_ALL);\n";
        $configContent .= "    ini_set('display_errors', 1);\n";
        $configContent .= "} else {\n";
        $configContent .= "    error_reporting(0);\n";
        $configContent .= "    ini_set('display_errors', 0);\n";
        $configContent .= "}\n";

        file_put_contents('../config/config.php', $configContent);
    }

    private function setFilePermissions() {
        $directories = [
            '../logs' => 0755,
            '../uploads' => 0755,
            '../cache' => 0755,
            '../config' => 0755
        ];

        foreach ($directories as $dir => $permission) {
            if (is_dir($dir)) {
                chmod($dir, $permission);
            }
        }

        $files = [
            '../config/config.php' => 0644,
            '../.htaccess' => 0644
        ];

        foreach ($files as $file => $permission) {
            if (file_exists($file)) {
                chmod($file, $permission);
            }
        }
    }

    private function getCurrentUrl() {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'];
        $path = dirname($_SERVER['REQUEST_URI']);
        $path = rtrim(str_replace('/install', '', $path), '/');
        return $protocol . '://' . $host . $path;
    }

    private function redirect($step) {
        header("Location: ?step=$step");
        exit;
    }

    private function renderHeader() {
        ?>
        <!DOCTYPE html>
        <html lang="ru">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Установка AstroGenix - <?php echo $this->steps[$this->currentStep]; ?></title>
            <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
            <style>
                :root {
                    --primary-color: #10b981;
                    --primary-dark: #059669;
                    --secondary-color: #8b5cf6;
                    --secondary-dark: #7c3aed;
                    --success-color: #10b981;
                    --warning-color: #f59e0b;
                    --danger-color: #ef4444;
                    --light-bg: #f8fafc;
                    --white: #ffffff;
                    --gray-100: #f1f5f9;
                    --gray-200: #e2e8f0;
                    --gray-300: #cbd5e1;
                    --gray-600: #475569;
                    --gray-800: #1e293b;
                    --gray-900: #0f172a;
                }

                * {
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }

                body {
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
                    min-height: 100vh;
                    color: var(--gray-800);
                }

                .installer-container {
                    max-width: 800px;
                    margin: 0 auto;
                    padding: 20px;
                }

                .installer-header {
                    text-align: center;
                    margin-bottom: 30px;
                    color: white;
                }

                .installer-header h1 {
                    font-size: 2.5rem;
                    margin-bottom: 10px;
                    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
                }

                .progress-bar {
                    background: rgba(255,255,255,0.2);
                    border-radius: 10px;
                    padding: 5px;
                    margin: 20px 0;
                }

                .progress-steps {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }

                .progress-step {
                    flex: 1;
                    text-align: center;
                    padding: 10px 5px;
                    color: rgba(255,255,255,0.7);
                    font-size: 0.9rem;
                    position: relative;
                }

                .progress-step.active {
                    color: white;
                    font-weight: bold;
                }

                .progress-step.completed {
                    color: var(--success-color);
                }

                .installer-card {
                    background: white;
                    border-radius: 15px;
                    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
                    overflow: hidden;
                    margin-bottom: 20px;
                }

                .step-content {
                    padding: 40px;
                }

                .welcome-hero {
                    text-align: center;
                    margin-bottom: 40px;
                }

                .eco-icon {
                    font-size: 4rem;
                    margin-bottom: 20px;
                }

                .features-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                    gap: 20px;
                    margin: 30px 0;
                }

                .feature-card {
                    text-align: center;
                    padding: 20px;
                    background: var(--gray-100);
                    border-radius: 10px;
                }

                .feature-card i {
                    font-size: 2rem;
                    color: var(--primary-color);
                    margin-bottom: 10px;
                }

                .installation-info {
                    background: var(--light-bg);
                    padding: 20px;
                    border-radius: 10px;
                    margin: 20px 0;
                }

                .installation-info ul {
                    list-style: none;
                    padding-left: 0;
                }

                .installation-info li {
                    padding: 5px 0;
                }

                .requirements-list {
                    margin: 20px 0;
                }

                .requirement-item {
                    display: flex;
                    align-items: center;
                    padding: 15px;
                    margin: 10px 0;
                    border-radius: 8px;
                    background: var(--gray-100);
                }

                .requirement-item.success {
                    background: #dcfce7;
                    border-left: 4px solid var(--success-color);
                }

                .requirement-item.warning {
                    background: #fef3c7;
                    border-left: 4px solid var(--warning-color);
                }

                .requirement-item.error {
                    background: #fee2e2;
                    border-left: 4px solid var(--danger-color);
                }

                .requirement-icon {
                    margin-right: 15px;
                    font-size: 1.2rem;
                }

                .requirement-details h4 {
                    margin-bottom: 5px;
                }

                .form-group {
                    margin-bottom: 20px;
                }

                .form-group label {
                    display: block;
                    margin-bottom: 5px;
                    font-weight: 600;
                    color: var(--gray-800);
                }

                .form-group input {
                    width: 100%;
                    padding: 12px;
                    border: 2px solid var(--gray-200);
                    border-radius: 8px;
                    font-size: 1rem;
                    transition: border-color 0.3s;
                }

                .form-group input:focus {
                    outline: none;
                    border-color: var(--primary-color);
                }

                .form-group small {
                    display: block;
                    margin-top: 5px;
                    color: var(--gray-600);
                    font-size: 0.9rem;
                }

                .btn {
                    display: inline-block;
                    padding: 12px 24px;
                    border: none;
                    border-radius: 8px;
                    font-size: 1rem;
                    font-weight: 600;
                    text-decoration: none;
                    cursor: pointer;
                    transition: all 0.3s;
                    margin: 5px;
                }

                .btn-primary {
                    background: var(--primary-color);
                    color: white;
                }

                .btn-primary:hover {
                    background: var(--primary-dark);
                    transform: translateY(-2px);
                }

                .btn-secondary {
                    background: var(--gray-600);
                    color: white;
                }

                .btn-secondary:hover {
                    background: var(--gray-800);
                }

                .btn-success {
                    background: var(--success-color);
                    color: white;
                }

                .btn-info {
                    background: var(--secondary-color);
                    color: white;
                }

                .btn-lg {
                    padding: 15px 30px;
                    font-size: 1.1rem;
                }

                .btn:disabled {
                    opacity: 0.6;
                    cursor: not-allowed;
                }

                .step-actions {
                    text-align: center;
                    margin-top: 30px;
                    padding-top: 20px;
                    border-top: 1px solid var(--gray-200);
                }

                .alert {
                    padding: 15px;
                    border-radius: 8px;
                    margin: 20px 0;
                }

                .alert-success {
                    background: #dcfce7;
                    color: #166534;
                    border: 1px solid #bbf7d0;
                }

                .alert-warning {
                    background: #fef3c7;
                    color: #92400e;
                    border: 1px solid #fde68a;
                }

                .alert-danger {
                    background: #fee2e2;
                    color: #991b1b;
                    border: 1px solid #fecaca;
                }

                .installation-progress {
                    margin: 20px 0;
                }

                .install-step {
                    display: flex;
                    align-items: center;
                    padding: 15px;
                    margin: 10px 0;
                    border-radius: 8px;
                    background: var(--gray-100);
                }

                .install-step.success {
                    background: #dcfce7;
                }

                .install-step.error {
                    background: #fee2e2;
                }

                .step-icon {
                    margin-right: 15px;
                    font-size: 1.2rem;
                }

                .completion-hero {
                    text-align: center;
                    margin-bottom: 40px;
                }

                .success-icon {
                    font-size: 4rem;
                    margin-bottom: 20px;
                }

                .completion-info {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                    gap: 20px;
                    margin: 30px 0;
                }

                .info-card {
                    background: var(--light-bg);
                    padding: 20px;
                    border-radius: 10px;
                }

                .info-card h3 {
                    margin-bottom: 15px;
                    color: var(--primary-color);
                }

                .info-card ul {
                    list-style: none;
                    padding-left: 0;
                }

                .info-card li {
                    padding: 5px 0;
                    color: var(--gray-600);
                }

                .quick-links {
                    text-align: center;
                    margin: 30px 0;
                }

                .support-info {
                    text-align: center;
                    margin-top: 30px;
                    padding: 20px;
                    background: var(--light-bg);
                    border-radius: 10px;
                }

                .text-success { color: var(--success-color); }
                .text-warning { color: var(--warning-color); }
                .text-danger { color: var(--danger-color); }

                @media (max-width: 768px) {
                    .installer-container {
                        padding: 10px;
                    }

                    .step-content {
                        padding: 20px;
                    }

                    .features-grid {
                        grid-template-columns: 1fr;
                    }

                    .completion-info {
                        grid-template-columns: 1fr;
                    }

                    .quick-links .btn {
                        display: block;
                        margin: 10px 0;
                    }
                }
            </style>
        </head>
        <body>
            <div class="installer-container">
                <div class="installer-header">
                    <h1><i class="fas fa-seedling"></i> AstroGenix</h1>
                    <p>Мастер установки платформы экологического майнинга</p>

                    <div class="progress-bar">
                        <div class="progress-steps">
                            <?php foreach ($this->steps as $key => $name): ?>
                                <div class="progress-step <?php
                                    echo $key === $this->currentStep ? 'active' : '';
                                    echo array_search($key, array_keys($this->steps)) < array_search($this->currentStep, array_keys($this->steps)) ? ' completed' : '';
                                ?>">
                                    <?php echo $name; ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>

                <div class="installer-card">
        <?php
    }

    private function renderFooter() {
        ?>
                </div>
            </div>
        </body>
        </html>
        <?php
    }
}

// Run the installer
$installer = new AstroGenixInstaller();
$installer->run();
?>
