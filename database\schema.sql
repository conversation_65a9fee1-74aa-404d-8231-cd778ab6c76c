-- AstroGenix Eco-Mining Platform Database Schema
CREATE DATABASE IF NOT EXISTS astrogenix_mining;
USE astrogenix_mining;

-- Users table with referral system
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    balance DECIMAL(15,2) DEFAULT 0.00,
    usdt_balance DECIMAL(15,2) DEFAULT 0.00,
    is_admin BOOLEAN DEFAULT FALSE,
    role ENUM('user', 'admin') DEFAULT 'user',
    status ENUM('active', 'suspended') DEFAULT 'active',
    verification_status ENUM('unverified', 'pending', 'verified', 'rejected') DEFAULT 'unverified',
    referral_code VARCHAR(20) UNIQUE NOT NULL,
    referred_by INT NULL,
    total_referrals INT DEFAULT 0,
    referral_earnings DECIMAL(15,2) DEFAULT 0.00,
    mining_power DECIMAL(10,2) DEFAULT 0.00,
    eco_score INT DEFAULT 0,
    level INT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    reset_token VARCHAR(255) NULL,
    reset_expires DATETIME NULL,
    FOREIGN KEY (referred_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Eco-Mining Investment Plans
CREATE TABLE mining_plans (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    category ENUM('solar_mining', 'wind_mining', 'hydro_mining', 'geothermal_mining', 'biomass_mining', 'hybrid_mining') NOT NULL DEFAULT 'solar_mining',
    min_amount DECIMAL(15,2) NOT NULL DEFAULT 10.00,
    max_amount DECIMAL(15,2) NULL,
    daily_rate DECIMAL(5,4) NOT NULL,
    duration_days INT NOT NULL DEFAULT 30,
    mining_power_bonus DECIMAL(5,2) DEFAULT 0.00,
    eco_score_bonus INT DEFAULT 0,
    image_url VARCHAR(500),
    icon_class VARCHAR(100) DEFAULT 'fas fa-leaf',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- User mining investments table
CREATE TABLE user_mining_investments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    mining_plan_id INT NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    daily_rate DECIMAL(5,4) NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    total_profit DECIMAL(15,2) DEFAULT 0.00,
    mining_power_gained DECIMAL(10,2) DEFAULT 0.00,
    eco_score_gained INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (mining_plan_id) REFERENCES mining_plans(id) ON DELETE CASCADE
);

-- Transactions table (deposits and withdrawals)
CREATE TABLE transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    type ENUM('deposit', 'withdrawal') NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    transaction_hash VARCHAR(255) NULL,
    wallet_address VARCHAR(255) NULL,
    screenshot_path VARCHAR(500) NULL,
    admin_notes TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Daily mining profits table
CREATE TABLE daily_mining_profits (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_investment_id INT NOT NULL,
    profit_amount DECIMAL(15,2) NOT NULL,
    profit_date DATE NOT NULL,
    mining_power_used DECIMAL(10,2) DEFAULT 0.00,
    eco_impact_score INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_investment_id) REFERENCES user_mining_investments(id) ON DELETE CASCADE,
    UNIQUE KEY unique_daily_profit (user_investment_id, profit_date)
);

-- Blog posts table
CREATE TABLE blog_posts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    content TEXT NOT NULL,
    excerpt TEXT,
    category VARCHAR(50) DEFAULT 'general',
    tags TEXT,
    meta_title VARCHAR(255),
    meta_description TEXT,
    image_url VARCHAR(500),
    is_published BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Contact messages table
CREATE TABLE contact_messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL,
    subject VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- User verifications table
CREATE TABLE user_verifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    birth_date DATE NOT NULL,
    passport_photo_path VARCHAR(500) NOT NULL,
    status ENUM('pending', 'verified', 'rejected') DEFAULT 'pending',
    admin_notes TEXT NULL,
    submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP NULL,
    processed_by INT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (processed_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Insert sample admin user (password: admin123)
INSERT INTO users (username, email, password_hash, is_admin, role, status, balance) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', TRUE, 'admin', 'active', 0.00);

-- Insert sample investment assets
INSERT INTO investments (title, description, category, min_amount, max_amount, monthly_rate, duration_months, image_url) VALUES
('Luxury Miami Penthouse', 'Premium oceanfront penthouse with stunning views and high-end amenities. Perfect for generating consistent rental income in the heart of Miami.', 'real_estate', 1000.00, 50000.00, 8.50, 24, 'https://images.unsplash.com/photo-1512917774080-9991f1c4c750?w=400'),
('Monaco Yacht Charter', 'Exclusive 120ft luxury yacht available for premium charter services. High-demand location with excellent returns.', 'other', 2500.00, 100000.00, 12.00, 18, 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=400'),
('Dubai Marina Tower', 'High-rise luxury apartment in the heart of Dubai Marina with stunning views and modern amenities.', 'real_estate', 500.00, 25000.00, 7.25, 36, 'https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?w=400'),
('Caribbean Sailing Yacht', 'Beautiful 80ft sailing yacht perfect for Caribbean charters. Year-round demand and experienced crew included.', 'other', 1500.00, 75000.00, 10.50, 24, 'https://images.unsplash.com/photo-1567899378494-47b22a2ae96a?w=400'),
('Tech Stock Portfolio', 'Diversified portfolio of leading technology stocks with strong growth potential and dividend yields.', 'stocks', 100.00, 10000.00, 6.75, 12, 'https://images.unsplash.com/photo-1611974789855-9c2a0a7236a3?w=400'),
('Cryptocurrency Fund', 'Managed cryptocurrency investment fund focusing on established coins with strong fundamentals.', 'crypto', 250.00, 15000.00, 15.00, 6, 'https://images.unsplash.com/photo-1621761191319-c6fb62004040?w=400');

-- Site settings table
CREATE TABLE IF NOT EXISTS site_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT,
    setting_type ENUM('text', 'number', 'boolean', 'json') DEFAULT 'text',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Balance changes log table
CREATE TABLE IF NOT EXISTS balance_changes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    admin_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    operation ENUM('add', 'subtract') NOT NULL,
    notes TEXT,
    old_balance DECIMAL(10,2) NOT NULL,
    new_balance DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Referral system table
CREATE TABLE referral_earnings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    referrer_id INT NOT NULL,
    referred_id INT NOT NULL,
    level INT NOT NULL DEFAULT 1,
    commission_rate DECIMAL(5,2) NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    source_type ENUM('investment', 'task_completion', 'bonus') NOT NULL,
    source_id INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (referrer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (referred_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Tasks/Missions system
CREATE TABLE tasks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    task_type ENUM('daily', 'weekly', 'monthly', 'one_time', 'referral', 'investment') NOT NULL,
    reward_type ENUM('usdt', 'mining_power', 'eco_score', 'level_xp') NOT NULL,
    reward_amount DECIMAL(15,2) NOT NULL,
    requirements JSON NULL,
    max_completions INT DEFAULT 1,
    is_active BOOLEAN DEFAULT TRUE,
    start_date DATE NULL,
    end_date DATE NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- User task completions
CREATE TABLE user_task_completions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    task_id INT NOT NULL,
    completed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    reward_claimed BOOLEAN DEFAULT FALSE,
    completion_data JSON NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_task (user_id, task_id)
);

-- User rankings and leaderboards
CREATE TABLE user_rankings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    ranking_type ENUM('total_investment', 'referral_count', 'eco_score', 'mining_power', 'monthly_profit') NOT NULL,
    score DECIMAL(15,2) NOT NULL,
    rank_position INT NOT NULL,
    period_start DATE NOT NULL,
    period_end DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_ranking (user_id, ranking_type, period_start)
);

-- Eco-mining statistics
CREATE TABLE eco_mining_stats (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    total_energy_generated DECIMAL(15,4) DEFAULT 0.00,
    carbon_offset_kg DECIMAL(10,2) DEFAULT 0.00,
    trees_planted_equivalent INT DEFAULT 0,
    renewable_energy_percentage DECIMAL(5,2) DEFAULT 0.00,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Insert sample blog posts
-- Insert sample eco-mining plans
INSERT INTO mining_plans (title, description, category, min_amount, max_amount, daily_rate, duration_days, mining_power_bonus, eco_score_bonus, image_url, icon_class) VALUES
('Solar Power Mining', 'Harness the power of the sun for sustainable cryptocurrency mining. Our solar-powered mining farms generate clean energy while producing consistent returns.', 'solar_mining', 50.00, 10000.00, 0.0150, 30, 1.20, 10, '/assets/images/solar-mining.jpg', 'fas fa-sun'),
('Wind Energy Mining', 'Utilize wind turbines to power eco-friendly mining operations. Clean, renewable energy meets profitable cryptocurrency generation.', 'wind_mining', 100.00, 15000.00, 0.0180, 45, 1.50, 15, '/assets/images/wind-mining.jpg', 'fas fa-wind'),
('Hydro Power Mining', 'Water-powered mining facilities that combine environmental responsibility with high-yield returns. Sustainable and profitable.', 'hydro_mining', 200.00, 25000.00, 0.0200, 60, 2.00, 20, '/assets/images/hydro-mining.jpg', 'fas fa-water'),
('Geothermal Mining', 'Tap into Earth\'s natural heat for continuous, eco-friendly mining operations. Stable returns with minimal environmental impact.', 'geothermal_mining', 500.00, 50000.00, 0.0250, 90, 3.00, 30, '/assets/images/geothermal-mining.jpg', 'fas fa-mountain'),
('Biomass Energy Mining', 'Convert organic waste into clean energy for sustainable mining. Turn environmental solutions into profitable investments.', 'biomass_mining', 300.00, 20000.00, 0.0220, 75, 2.50, 25, '/assets/images/biomass-mining.jpg', 'fas fa-leaf'),
('Hybrid Eco Mining', 'Multi-source renewable energy mining combining solar, wind, and hydro power for maximum efficiency and returns.', 'hybrid_mining', 1000.00, 100000.00, 0.0300, 120, 5.00, 50, '/assets/images/hybrid-mining.jpg', 'fas fa-bolt');

-- Insert sample tasks
INSERT INTO tasks (title, description, task_type, reward_type, reward_amount, requirements, max_completions) VALUES
('Daily Login Bonus', 'Login to your AstroGenix account daily to receive USDT rewards', 'daily', 'usdt', 1.00, '{"action": "login"}', 1),
('First Investment', 'Make your first eco-mining investment to unlock bonus rewards', 'one_time', 'usdt', 25.00, '{"action": "invest", "min_amount": 50}', 1),
('Refer a Friend', 'Invite friends to join AstroGenix and earn referral bonuses', 'referral', 'usdt', 10.00, '{"action": "referral", "min_investment": 100}', 999),
('Weekly Eco Challenge', 'Complete weekly eco-mining activities to boost your eco score', 'weekly', 'eco_score', 50.00, '{"action": "mining_activity", "min_power": 10}', 1),
('Monthly Mining Master', 'Achieve top mining performance this month', 'monthly', 'mining_power', 100.00, '{"action": "top_performance", "rank": 10}', 1);

-- Insert sample blog posts
INSERT INTO blog_posts (title, slug, content, excerpt, image_url, is_published) VALUES
('The Future of Eco-Mining: Sustainable Cryptocurrency', 'future-eco-mining-sustainable-crypto', 'Discover how AstroGenix is revolutionizing cryptocurrency mining through renewable energy sources. Our innovative approach combines environmental responsibility with profitable returns, creating a sustainable future for digital assets.', 'Learn about sustainable cryptocurrency mining and its environmental benefits.', '/assets/images/blog-eco-mining.jpg', TRUE),
('Solar Power Mining: Harnessing the Sun for Profit', 'solar-power-mining-profit', 'Solar-powered mining represents the future of sustainable cryptocurrency generation. With decreasing solar panel costs and increasing efficiency, solar mining offers both environmental benefits and strong financial returns for investors.', 'Exploring solar-powered cryptocurrency mining opportunities.', '/assets/images/blog-solar.jpg', TRUE),
('Building a Greener Future with Renewable Mining', 'greener-future-renewable-mining', 'AstroGenix is leading the charge in eco-friendly mining solutions. By utilizing wind, solar, hydro, and geothermal energy, we are creating a sustainable ecosystem that benefits both investors and the environment.', 'How renewable energy is transforming the mining industry.', '/assets/images/blog-renewable.jpg', TRUE);
